"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx":
/*!****************************************************************************!*\
  !*** ./frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx ***!
  \****************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SecondaryButton: () => (/* binding */ SecondaryButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../style/style_secondary */ \"(app-pages-browser)/./frontend/ButtonCodes/secondary/style/style_secondary.ts\");\n/* __next_internal_client_entry_do_not_use__ SecondaryButton auto */ \nvar _s = $RefreshSig$();\n\n\n// 普通按键组件\nconst SecondaryButton = (param)=>{\n    let { mode = 'toggle', initialActive = false, text = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_BUTTON_TEXT, onClick, onMouseEnter, onMouseLeave, onMouseDown, onMouseUp, customStyles = {}, disabled = false } = param;\n    _s();\n    // 内部状态管理\n    const [isActive, setIsActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialActive);\n    const [isHovered, setIsHovered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPressed, setIsPressed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 同步外部 initialActive 属性的变化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SecondaryButton.useEffect\": ()=>{\n            setIsActive(initialActive);\n        }\n    }[\"SecondaryButton.useEffect\"], [\n        initialActive\n    ]);\n    // 计算当前样式\n    const currentStyles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SecondaryButton.useMemo[currentStyles]\": ()=>{\n            const baseStyles = {\n                ..._style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.default\n            };\n            let backgroundColor = baseStyles.backgroundColor;\n            if (mode === 'toggle') {\n                // 持续状态模式\n                if (isActive) {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.active.backgroundColor;\n                    if (isHovered && !disabled) {\n                        backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.active.hover;\n                    }\n                } else {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.inactive.backgroundColor;\n                    if (isHovered && !disabled) {\n                        backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.inactive.hover;\n                    }\n                }\n            } else if (mode === 'instant') {\n                // 瞬时状态模式\n                if (isPressed && !disabled) {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.instant.active;\n                } else if (isHovered && !disabled) {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.instant.hover;\n                } else {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.instant.default;\n                }\n            }\n            // 应用禁用状态的样式修改\n            const finalStyles = {\n                ...baseStyles,\n                ...customStyles,\n                backgroundColor\n            };\n            if (disabled) {\n                finalStyles.cursor = 'not-allowed';\n                finalStyles.opacity = 0.8; // 稍微降低透明度但仍能看清激活颜色\n            }\n            return finalStyles;\n        }\n    }[\"SecondaryButton.useMemo[currentStyles]\"], [\n        mode,\n        isActive,\n        isHovered,\n        isPressed,\n        disabled,\n        customStyles\n    ]);\n    // 事件处理函数\n    const handleClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleClick]\": ()=>{\n            if (disabled) return;\n            if (mode === 'toggle') {\n                // 直接在组件中处理toggle逻辑\n                const newActiveState = !isActive;\n                setIsActive(newActiveState);\n                onClick === null || onClick === void 0 ? void 0 : onClick(newActiveState);\n            } else if (mode === 'instant') {\n                // instant模式\n                setIsActive(true);\n                onClick === null || onClick === void 0 ? void 0 : onClick(true);\n            }\n        }\n    }[\"SecondaryButton.useCallback[handleClick]\"], [\n        disabled,\n        mode,\n        isActive,\n        onClick\n    ]);\n    const handleMouseEnter = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseEnter]\": ()=>{\n            if (disabled) return;\n            setIsHovered(true);\n            onMouseEnter === null || onMouseEnter === void 0 ? void 0 : onMouseEnter();\n        }\n    }[\"SecondaryButton.useCallback[handleMouseEnter]\"], [\n        disabled,\n        onMouseEnter\n    ]);\n    const handleMouseLeave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseLeave]\": ()=>{\n            if (disabled) return;\n            setIsHovered(false);\n            setIsPressed(false);\n            onMouseLeave === null || onMouseLeave === void 0 ? void 0 : onMouseLeave();\n        }\n    }[\"SecondaryButton.useCallback[handleMouseLeave]\"], [\n        disabled,\n        onMouseLeave\n    ]);\n    const handleMouseDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseDown]\": ()=>{\n            if (disabled) return;\n            setIsPressed(true);\n            if (mode === 'instant') {\n                setIsActive(true);\n            }\n            onMouseDown === null || onMouseDown === void 0 ? void 0 : onMouseDown();\n        }\n    }[\"SecondaryButton.useCallback[handleMouseDown]\"], [\n        disabled,\n        mode,\n        onMouseDown\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseUp]\": ()=>{\n            if (disabled) return;\n            setIsPressed(false);\n            if (mode === 'instant') {\n                setIsActive(false);\n            }\n            onMouseUp === null || onMouseUp === void 0 ? void 0 : onMouseUp();\n        }\n    }[\"SecondaryButton.useCallback[handleMouseUp]\"], [\n        disabled,\n        mode,\n        onMouseUp\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        style: currentStyles,\n        onClick: handleClick,\n        onMouseEnter: handleMouseEnter,\n        onMouseLeave: handleMouseLeave,\n        onMouseDown: handleMouseDown,\n        onMouseUp: handleMouseUp,\n        disabled: disabled,\n        type: \"button\",\n        children: text\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx\",\n        lineNumber: 151,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SecondaryButton, \"jDVrFDvB/QjlX759vbmqFyHRgHI=\");\n_c = SecondaryButton;\nvar _c;\n$RefreshReg$(_c, \"SecondaryButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx\n"));

/***/ })

});