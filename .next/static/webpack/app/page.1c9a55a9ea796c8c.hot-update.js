"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx":
/*!****************************************************************************!*\
  !*** ./frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx ***!
  \****************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SecondaryButton: () => (/* binding */ SecondaryButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../style/style_secondary */ \"(app-pages-browser)/./frontend/ButtonCodes/secondary/style/style_secondary.ts\");\n/* harmony import */ var _event_event_secondary__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../event/event_secondary */ \"(app-pages-browser)/./frontend/ButtonCodes/secondary/event/event_secondary.ts\");\n/* __next_internal_client_entry_do_not_use__ SecondaryButton auto */ \nvar _s = $RefreshSig$();\n\n\n\n// 普通按键组件\nconst SecondaryButton = (param)=>{\n    let { mode = 'toggle', initialActive = false, text = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_BUTTON_TEXT, onClick, onMouseEnter, onMouseLeave, onMouseDown, onMouseUp, customStyles = {}, disabled = false } = param;\n    _s();\n    // 内部状态管理\n    const [isActive, setIsActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialActive);\n    const [isHovered, setIsHovered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPressed, setIsPressed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 创建事件处理器\n    const eventHandler = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SecondaryButton.useMemo[eventHandler]\": ()=>{\n            return (0,_event_event_secondary__WEBPACK_IMPORTED_MODULE_3__.createSecondaryButtonEventHandler)(mode, {\n                onClick: {\n                    \"SecondaryButton.useMemo[eventHandler]\": (active)=>{\n                        setIsActive(active);\n                        onClick === null || onClick === void 0 ? void 0 : onClick(active);\n                    }\n                }[\"SecondaryButton.useMemo[eventHandler]\"],\n                onMouseEnter,\n                onMouseLeave,\n                onMouseDown,\n                onMouseUp\n            }, initialActive);\n        }\n    }[\"SecondaryButton.useMemo[eventHandler]\"], [\n        mode,\n        onClick,\n        onMouseEnter,\n        onMouseLeave,\n        onMouseDown,\n        onMouseUp,\n        initialActive\n    ]);\n    // 计算当前样式\n    const currentStyles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SecondaryButton.useMemo[currentStyles]\": ()=>{\n            const baseStyles = {\n                ..._style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.default\n            };\n            if (disabled) {\n                return {\n                    ...baseStyles,\n                    ...customStyles,\n                    opacity: 0.6,\n                    cursor: 'not-allowed'\n                };\n            }\n            let backgroundColor = baseStyles.backgroundColor;\n            if (mode === 'toggle') {\n                // 持续状态模式\n                if (isActive) {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.active.backgroundColor;\n                    if (isHovered) {\n                        backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.active.hover;\n                    }\n                } else {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.inactive.backgroundColor;\n                    if (isHovered) {\n                        backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.inactive.hover;\n                    }\n                }\n            } else if (mode === 'instant') {\n                // 瞬时状态模式\n                if (isPressed) {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.instant.active;\n                } else if (isHovered) {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.instant.hover;\n                } else {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.instant.default;\n                }\n            }\n            return {\n                ...baseStyles,\n                ...customStyles,\n                backgroundColor\n            };\n        }\n    }[\"SecondaryButton.useMemo[currentStyles]\"], [\n        mode,\n        isActive,\n        isHovered,\n        isPressed,\n        disabled,\n        customStyles\n    ]);\n    // 事件处理函数\n    const handleClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleClick]\": ()=>{\n            if (disabled) return;\n            const newActiveState = eventHandler.handleClick();\n            setIsActive(newActiveState);\n        }\n    }[\"SecondaryButton.useCallback[handleClick]\"], [\n        disabled,\n        eventHandler\n    ]);\n    const handleMouseEnter = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseEnter]\": ()=>{\n            if (disabled) return;\n            setIsHovered(true);\n            eventHandler.handleMouseEnter();\n        }\n    }[\"SecondaryButton.useCallback[handleMouseEnter]\"], [\n        disabled,\n        eventHandler\n    ]);\n    const handleMouseLeave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseLeave]\": ()=>{\n            if (disabled) return;\n            setIsHovered(false);\n            setIsPressed(false);\n            eventHandler.handleMouseLeave();\n        }\n    }[\"SecondaryButton.useCallback[handleMouseLeave]\"], [\n        disabled,\n        eventHandler\n    ]);\n    const handleMouseDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseDown]\": ()=>{\n            if (disabled) return;\n            setIsPressed(true);\n            eventHandler.handleMouseDown();\n        }\n    }[\"SecondaryButton.useCallback[handleMouseDown]\"], [\n        disabled,\n        eventHandler\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseUp]\": ()=>{\n            if (disabled) return;\n            setIsPressed(false);\n            eventHandler.handleMouseUp();\n        }\n    }[\"SecondaryButton.useCallback[handleMouseUp]\"], [\n        disabled,\n        eventHandler\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        style: currentStyles,\n        onClick: handleClick,\n        onMouseEnter: handleMouseEnter,\n        onMouseLeave: handleMouseLeave,\n        onMouseDown: handleMouseDown,\n        onMouseUp: handleMouseUp,\n        disabled: disabled,\n        type: \"button\",\n        children: text\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx\",\n        lineNumber: 149,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SecondaryButton, \"Hu7mUcVmi51WF3SCpAOMp6qdv/I=\");\n_c = SecondaryButton;\nvar _c;\n$RefreshReg$(_c, \"SecondaryButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx\n"));

/***/ })

});