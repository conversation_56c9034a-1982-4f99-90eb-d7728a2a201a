"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx":
/*!****************************************************************************!*\
  !*** ./frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx ***!
  \****************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SecondaryButton: () => (/* binding */ SecondaryButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../style/style_secondary */ \"(app-pages-browser)/./frontend/ButtonCodes/secondary/style/style_secondary.ts\");\n/* harmony import */ var _event_event_secondary__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../event/event_secondary */ \"(app-pages-browser)/./frontend/ButtonCodes/secondary/event/event_secondary.ts\");\n/* __next_internal_client_entry_do_not_use__ SecondaryButton auto */ \nvar _s = $RefreshSig$();\n\n\n\n// 普通按键组件\nconst SecondaryButton = (param)=>{\n    let { mode = 'toggle', initialActive = false, text = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_BUTTON_TEXT, onClick, onMouseEnter, onMouseLeave, onMouseDown, onMouseUp, customStyles = {}, disabled = false } = param;\n    _s();\n    // 内部状态管理\n    const [isActive, setIsActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialActive);\n    const [isHovered, setIsHovered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPressed, setIsPressed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 创建事件处理器\n    const eventHandler = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SecondaryButton.useMemo[eventHandler]\": ()=>{\n            return (0,_event_event_secondary__WEBPACK_IMPORTED_MODULE_3__.createSecondaryButtonEventHandler)(mode, {\n                onClick: {\n                    \"SecondaryButton.useMemo[eventHandler]\": (active)=>{\n                        setIsActive(active);\n                        onClick === null || onClick === void 0 ? void 0 : onClick(active);\n                    }\n                }[\"SecondaryButton.useMemo[eventHandler]\"],\n                onMouseEnter,\n                onMouseLeave,\n                onMouseDown,\n                onMouseUp\n            });\n        }\n    }[\"SecondaryButton.useMemo[eventHandler]\"], [\n        mode,\n        onClick,\n        onMouseEnter,\n        onMouseLeave,\n        onMouseDown,\n        onMouseUp\n    ]);\n    // 计算当前样式\n    const currentStyles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SecondaryButton.useMemo[currentStyles]\": ()=>{\n            const baseStyles = {\n                ..._style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.default\n            };\n            if (disabled) {\n                return {\n                    ...baseStyles,\n                    ...customStyles,\n                    opacity: 0.6,\n                    cursor: 'not-allowed'\n                };\n            }\n            let backgroundColor = baseStyles.backgroundColor;\n            if (mode === 'toggle') {\n                // 持续状态模式\n                if (isActive) {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.active.backgroundColor;\n                    if (isHovered) {\n                        backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.active.hover;\n                    }\n                } else {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.inactive.backgroundColor;\n                    if (isHovered) {\n                        backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.inactive.hover;\n                    }\n                }\n            } else if (mode === 'instant') {\n                // 瞬时状态模式\n                if (isPressed) {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.instant.active;\n                } else if (isHovered) {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.instant.hover;\n                } else {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.instant.default;\n                }\n            }\n            return {\n                ...baseStyles,\n                ...customStyles,\n                backgroundColor\n            };\n        }\n    }[\"SecondaryButton.useMemo[currentStyles]\"], [\n        mode,\n        isActive,\n        isHovered,\n        isPressed,\n        disabled,\n        customStyles\n    ]);\n    // 事件处理函数\n    const handleClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleClick]\": ()=>{\n            if (disabled) return;\n            const newActiveState = eventHandler.handleClick();\n            setIsActive(newActiveState);\n        }\n    }[\"SecondaryButton.useCallback[handleClick]\"], [\n        disabled,\n        eventHandler\n    ]);\n    const handleMouseEnter = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseEnter]\": ()=>{\n            if (disabled) return;\n            setIsHovered(true);\n            eventHandler.handleMouseEnter();\n        }\n    }[\"SecondaryButton.useCallback[handleMouseEnter]\"], [\n        disabled,\n        eventHandler\n    ]);\n    const handleMouseLeave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseLeave]\": ()=>{\n            if (disabled) return;\n            setIsHovered(false);\n            setIsPressed(false);\n            eventHandler.handleMouseLeave();\n        }\n    }[\"SecondaryButton.useCallback[handleMouseLeave]\"], [\n        disabled,\n        eventHandler\n    ]);\n    const handleMouseDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseDown]\": ()=>{\n            if (disabled) return;\n            setIsPressed(true);\n            eventHandler.handleMouseDown();\n        }\n    }[\"SecondaryButton.useCallback[handleMouseDown]\"], [\n        disabled,\n        eventHandler\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseUp]\": ()=>{\n            if (disabled) return;\n            setIsPressed(false);\n            eventHandler.handleMouseUp();\n        }\n    }[\"SecondaryButton.useCallback[handleMouseUp]\"], [\n        disabled,\n        eventHandler\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        style: currentStyles,\n        onClick: handleClick,\n        onMouseEnter: handleMouseEnter,\n        onMouseLeave: handleMouseLeave,\n        onMouseDown: handleMouseDown,\n        onMouseUp: handleMouseUp,\n        disabled: disabled,\n        type: \"button\",\n        children: text\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SecondaryButton, \"Hu7mUcVmi51WF3SCpAOMp6qdv/I=\");\n_c = SecondaryButton;\nvar _c;\n$RefreshReg$(_c, \"SecondaryButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx\n"));

/***/ })

});