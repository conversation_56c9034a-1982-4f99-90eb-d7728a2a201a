"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx":
/*!****************************************************************************!*\
  !*** ./frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx ***!
  \****************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SecondaryButton: () => (/* binding */ SecondaryButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../style/style_secondary */ \"(app-pages-browser)/./frontend/ButtonCodes/secondary/style/style_secondary.ts\");\n/* __next_internal_client_entry_do_not_use__ SecondaryButton auto */ \nvar _s = $RefreshSig$();\n\n\n// 普通按键组件\nconst SecondaryButton = (param)=>{\n    let { mode = 'toggle', initialActive = false, text = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_BUTTON_TEXT, onClick, onMouseEnter, onMouseLeave, onMouseDown, onMouseUp, customStyles = {}, disabled = false } = param;\n    _s();\n    // 内部状态管理\n    const [isActive, setIsActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialActive);\n    const [isHovered, setIsHovered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPressed, setIsPressed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 计算当前样式\n    const currentStyles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SecondaryButton.useMemo[currentStyles]\": ()=>{\n            const baseStyles = {\n                ..._style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.default\n            };\n            let backgroundColor = baseStyles.backgroundColor;\n            if (mode === 'toggle') {\n                // 持续状态模式\n                if (isActive) {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.active.backgroundColor;\n                    if (isHovered && !disabled) {\n                        backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.active.hover;\n                    }\n                } else {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.inactive.backgroundColor;\n                    if (isHovered && !disabled) {\n                        backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.inactive.hover;\n                    }\n                }\n            } else if (mode === 'instant') {\n                // 瞬时状态模式\n                if (isPressed && !disabled) {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.instant.active;\n                } else if (isHovered && !disabled) {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.instant.hover;\n                } else {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.instant.default;\n                }\n            }\n            // 应用禁用状态的样式修改\n            const finalStyles = {\n                ...baseStyles,\n                ...customStyles,\n                backgroundColor\n            };\n            if (disabled) {\n                finalStyles.cursor = 'not-allowed';\n                finalStyles.opacity = 0.8; // 稍微降低透明度但仍能看清激活颜色\n            }\n            return finalStyles;\n        }\n    }[\"SecondaryButton.useMemo[currentStyles]\"], [\n        mode,\n        isActive,\n        isHovered,\n        isPressed,\n        disabled,\n        customStyles\n    ]);\n    // 事件处理函数\n    const handleClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleClick]\": ()=>{\n            if (disabled) return;\n            if (mode === 'toggle') {\n                // 直接在组件中处理toggle逻辑\n                const newActiveState = !isActive;\n                setIsActive(newActiveState);\n                onClick === null || onClick === void 0 ? void 0 : onClick(newActiveState);\n            } else if (mode === 'instant') {\n                // instant模式\n                setIsActive(true);\n                onClick === null || onClick === void 0 ? void 0 : onClick(true);\n            }\n        }\n    }[\"SecondaryButton.useCallback[handleClick]\"], [\n        disabled,\n        mode,\n        isActive,\n        onClick\n    ]);\n    const handleMouseEnter = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseEnter]\": ()=>{\n            if (disabled) return;\n            setIsHovered(true);\n            onMouseEnter === null || onMouseEnter === void 0 ? void 0 : onMouseEnter();\n        }\n    }[\"SecondaryButton.useCallback[handleMouseEnter]\"], [\n        disabled,\n        onMouseEnter\n    ]);\n    const handleMouseLeave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseLeave]\": ()=>{\n            if (disabled) return;\n            setIsHovered(false);\n            setIsPressed(false);\n            onMouseLeave === null || onMouseLeave === void 0 ? void 0 : onMouseLeave();\n        }\n    }[\"SecondaryButton.useCallback[handleMouseLeave]\"], [\n        disabled,\n        onMouseLeave\n    ]);\n    const handleMouseDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseDown]\": ()=>{\n            if (disabled) return;\n            setIsPressed(true);\n            if (mode === 'instant') {\n                setIsActive(true);\n            }\n            onMouseDown === null || onMouseDown === void 0 ? void 0 : onMouseDown();\n        }\n    }[\"SecondaryButton.useCallback[handleMouseDown]\"], [\n        disabled,\n        mode,\n        onMouseDown\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseUp]\": ()=>{\n            if (disabled) return;\n            setIsPressed(false);\n            if (mode === 'instant') {\n                setIsActive(false);\n            }\n            onMouseUp === null || onMouseUp === void 0 ? void 0 : onMouseUp();\n        }\n    }[\"SecondaryButton.useCallback[handleMouseUp]\"], [\n        disabled,\n        mode,\n        onMouseUp\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        style: currentStyles,\n        onClick: handleClick,\n        onMouseEnter: handleMouseEnter,\n        onMouseLeave: handleMouseLeave,\n        onMouseDown: handleMouseDown,\n        onMouseUp: handleMouseUp,\n        disabled: disabled,\n        type: \"button\",\n        children: text\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx\",\n        lineNumber: 146,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SecondaryButton, \"2aMrR/1EjmSpfkTNl4WrNQ7EKag=\");\n_c = SecondaryButton;\nvar _c;\n$RefreshReg$(_c, \"SecondaryButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx\n"));

/***/ })

});