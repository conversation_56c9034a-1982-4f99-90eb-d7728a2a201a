"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx":
/*!****************************************************************************!*\
  !*** ./frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx ***!
  \****************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SecondaryButton: () => (/* binding */ SecondaryButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../style/style_secondary */ \"(app-pages-browser)/./frontend/ButtonCodes/secondary/style/style_secondary.ts\");\n/* __next_internal_client_entry_do_not_use__ SecondaryButton auto */ \nvar _s = $RefreshSig$();\n\n\n// 普通按键组件\nconst SecondaryButton = (param)=>{\n    let { mode = 'toggle', initialActive = false, text = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_BUTTON_TEXT, onClick, onMouseEnter, onMouseLeave, onMouseDown, onMouseUp, customStyles = {}, disabled = false } = param;\n    _s();\n    // 内部状态管理\n    const [isActive, setIsActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialActive);\n    const [isHovered, setIsHovered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPressed, setIsPressed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 计算当前样式\n    const currentStyles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SecondaryButton.useMemo[currentStyles]\": ()=>{\n            const baseStyles = {\n                ..._style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.default\n            };\n            if (disabled) {\n                return {\n                    ...baseStyles,\n                    ...customStyles,\n                    opacity: 0.6,\n                    cursor: 'not-allowed'\n                };\n            }\n            let backgroundColor = baseStyles.backgroundColor;\n            if (mode === 'toggle') {\n                // 持续状态模式\n                if (isActive) {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.active.backgroundColor;\n                    if (isHovered) {\n                        backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.active.hover;\n                    }\n                } else {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.inactive.backgroundColor;\n                    if (isHovered) {\n                        backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.inactive.hover;\n                    }\n                }\n            } else if (mode === 'instant') {\n                // 瞬时状态模式\n                if (isPressed) {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.instant.active;\n                } else if (isHovered) {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.instant.hover;\n                } else {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.instant.default;\n                }\n            }\n            return {\n                ...baseStyles,\n                ...customStyles,\n                backgroundColor\n            };\n        }\n    }[\"SecondaryButton.useMemo[currentStyles]\"], [\n        mode,\n        isActive,\n        isHovered,\n        isPressed,\n        disabled,\n        customStyles\n    ]);\n    // 事件处理函数\n    const handleClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleClick]\": ()=>{\n            if (disabled) return;\n            if (mode === 'toggle') {\n                // 直接在组件中处理toggle逻辑\n                const newActiveState = !isActive;\n                setIsActive(newActiveState);\n                onClick === null || onClick === void 0 ? void 0 : onClick(newActiveState);\n            } else if (mode === 'instant') {\n                // instant模式\n                setIsActive(true);\n                onClick === null || onClick === void 0 ? void 0 : onClick(true);\n            }\n        }\n    }[\"SecondaryButton.useCallback[handleClick]\"], [\n        disabled,\n        mode,\n        isActive,\n        onClick\n    ]);\n    const handleMouseEnter = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseEnter]\": ()=>{\n            if (disabled) return;\n            setIsHovered(true);\n            onMouseEnter === null || onMouseEnter === void 0 ? void 0 : onMouseEnter();\n        }\n    }[\"SecondaryButton.useCallback[handleMouseEnter]\"], [\n        disabled,\n        onMouseEnter\n    ]);\n    const handleMouseLeave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseLeave]\": ()=>{\n            if (disabled) return;\n            setIsHovered(false);\n            setIsPressed(false);\n            onMouseLeave === null || onMouseLeave === void 0 ? void 0 : onMouseLeave();\n        }\n    }[\"SecondaryButton.useCallback[handleMouseLeave]\"], [\n        disabled,\n        onMouseLeave\n    ]);\n    const handleMouseDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseDown]\": ()=>{\n            if (disabled) return;\n            setIsPressed(true);\n            if (mode === 'instant') {\n                setIsActive(true);\n            }\n            onMouseDown === null || onMouseDown === void 0 ? void 0 : onMouseDown();\n        }\n    }[\"SecondaryButton.useCallback[handleMouseDown]\"], [\n        disabled,\n        mode,\n        onMouseDown\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseUp]\": ()=>{\n            if (disabled) return;\n            setIsPressed(false);\n            if (mode === 'instant') {\n                setIsActive(false);\n            }\n            onMouseUp === null || onMouseUp === void 0 ? void 0 : onMouseUp();\n        }\n    }[\"SecondaryButton.useCallback[handleMouseUp]\"], [\n        disabled,\n        mode,\n        onMouseUp\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        style: currentStyles,\n        onClick: handleClick,\n        onMouseEnter: handleMouseEnter,\n        onMouseLeave: handleMouseLeave,\n        onMouseDown: handleMouseDown,\n        onMouseUp: handleMouseUp,\n        disabled: disabled,\n        type: \"button\",\n        children: text\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SecondaryButton, \"2aMrR/1EjmSpfkTNl4WrNQ7EKag=\");\n_c = SecondaryButton;\nvar _c;\n$RefreshReg$(_c, \"SecondaryButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx\n"));

/***/ })

});