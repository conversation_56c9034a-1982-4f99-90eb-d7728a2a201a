"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./frontend/ButtonCodes/secondary/event/event_secondary.ts":
/*!*****************************************************************!*\
  !*** ./frontend/ButtonCodes/secondary/event/event_secondary.ts ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SecondaryButtonEventHandler: () => (/* binding */ SecondaryButtonEventHandler),\n/* harmony export */   createSecondaryButtonEventHandler: () => (/* binding */ createSecondaryButtonEventHandler)\n/* harmony export */ });\n// 普通按键事件类型定义\n// 普通按键事件处理类\nclass SecondaryButtonEventHandler {\n    // 获取当前状态\n    getState() {\n        return {\n            ...this.state\n        };\n    }\n    // 设置模式\n    setMode(mode) {\n        this.state.mode = mode;\n    }\n    // 设置激活状态\n    setActive(isActive) {\n        this.state.isActive = isActive;\n    }\n    // 处理点击事件\n    handleClick() {\n        if (this.state.mode === 'toggle') {\n            // 持续状态模式：切换并保持状态\n            this.state.isActive = !this.state.isActive;\n        } else if (this.state.mode === 'instant') {\n            // 瞬时状态模式：瞬间激活\n            this.state.isActive = true;\n        }\n        // 调用外部点击事件\n        if (this.events.onClick) {\n            this.events.onClick(this.state.isActive);\n        }\n        return this.state.isActive;\n    }\n    // 处理鼠标进入事件\n    handleMouseEnter() {\n        if (this.events.onMouseEnter) {\n            this.events.onMouseEnter();\n        }\n    }\n    // 处理鼠标离开事件\n    handleMouseLeave() {\n        if (this.events.onMouseLeave) {\n            this.events.onMouseLeave();\n        }\n    }\n    // 处理鼠标按下事件\n    handleMouseDown() {\n        if (this.state.mode === 'instant') {\n            this.state.isActive = true;\n        }\n        if (this.events.onMouseDown) {\n            this.events.onMouseDown();\n        }\n    }\n    // 处理鼠标松开事件\n    handleMouseUp() {\n        if (this.state.mode === 'instant') {\n            this.state.isActive = false;\n        }\n        if (this.events.onMouseUp) {\n            this.events.onMouseUp();\n        }\n    }\n    // 更新事件处理器\n    updateEvents(events) {\n        this.events = {\n            ...this.events,\n            ...events\n        };\n    }\n    constructor(initialState = {\n        isActive: false,\n        mode: 'toggle'\n    }, events = {}){\n        this.state = initialState;\n        this.events = events;\n    }\n}\n// 默认事件处理器工厂函数\nconst createSecondaryButtonEventHandler = function() {\n    let mode = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'toggle', events = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, initialActive = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n    return new SecondaryButtonEventHandler({\n        isActive: initialActive,\n        mode\n    }, events);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./frontend/ButtonCodes/secondary/event/event_secondary.ts\n"));

/***/ })

});