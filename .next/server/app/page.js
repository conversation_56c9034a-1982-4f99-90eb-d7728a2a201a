/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"bcac0b1c497f\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy96aGFvX3ppX2ZlbmcvRGVza3RvcC/ku6PnoIHmlofku7YvTHlyaWNXcml0aW5nUHJvamVjdC9hcHAvZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJiY2FjMGIxYzQ5N2ZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: 'LyricWriting Project',\n    description: 'A lyric writing application'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/app/layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/app/layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDc0I7QUFFZixNQUFNQSxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztzQkFDRUg7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIi9Vc2Vycy96aGFvX3ppX2ZlbmcvRGVza3RvcC/ku6PnoIHmlofku7YvTHlyaWNXcml0aW5nUHJvamVjdC9hcHAvbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdMeXJpY1dyaXRpbmcgUHJvamVjdCcsXG4gIGRlc2NyaXB0aW9uOiAnQSBseXJpYyB3cml0aW5nIGFwcGxpY2F0aW9uJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cInpoLUNOXCI+XG4gICAgICA8Ym9keT5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _frontend_componets_componet_main_container__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../frontend/componets/componet/main_container */ \"(rsc)/./frontend/componets/componet/main_container.tsx\");\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_frontend_componets_componet_main_container__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/app/page.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBeUU7QUFFMUQsU0FBU0M7SUFDdEIscUJBQU8sOERBQUNELG1GQUFhQTs7Ozs7QUFDdkIiLCJzb3VyY2VzIjpbIi9Vc2Vycy96aGFvX3ppX2ZlbmcvRGVza3RvcC/ku6PnoIHmlofku7YvTHlyaWNXcml0aW5nUHJvamVjdC9hcHAvcGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IE1haW5Db250YWluZXIgZnJvbSAnLi4vZnJvbnRlbmQvY29tcG9uZXRzL2NvbXBvbmV0L21haW5fY29udGFpbmVyJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lKCkge1xuICByZXR1cm4gPE1haW5Db250YWluZXIgLz5cbn1cbiJdLCJuYW1lcyI6WyJNYWluQ29udGFpbmVyIiwiSG9tZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./frontend/componets/componet/main_container.tsx":
/*!********************************************************!*\
  !*** ./frontend/componets/componet/main_container.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/componet/main_container.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/componet/main_container.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/app-render/interop-default */ \"(rsc)/./node_modules/next/dist/server/app-render/interop-default.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/experimental/ppr */ \"(rsc)/./node_modules/next/dist/server/lib/experimental/ppr.js\");\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/request/fallback-params */ \"(rsc)/./node_modules/next/dist/server/request/fallback-params.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/app-render/encryption-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/encryption-utils.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/lib/streaming-metadata */ \"(rsc)/./node_modules/next/dist/server/lib/streaming-metadata.js\");\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/app-render/action-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/action-utils.js\");\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/server/lib/server-action-request-meta */ \"(rsc)/./node_modules/next/dist/server/lib/server-action-request-meta.js\");\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/client/components/app-router-headers */ \"(rsc)/./node_modules/next/dist/client/components/app-router-headers.js\");\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/is-bot */ \"next/dist/shared/lib/router/utils/is-bot\");\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/dist/lib/fallback */ \"(rsc)/./node_modules/next/dist/lib/fallback.js\");\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! next/dist/server/render-result */ \"(rsc)/./node_modules/next/dist/server/render-result.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! next/dist/server/stream-utils/encoded-tags */ \"(rsc)/./node_modules/next/dist/server/stream-utils/encoded-tags.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! next/dist/server/send-payload */ \"(rsc)/./node_modules/next/dist/server/send-payload.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\");\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__);\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! next/dist/client/components/redirect-status-code */ \"(rsc)/./node_modules/next/dist/client/components/redirect-status-code.js\");\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\",\"handler\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/not-found.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/not-found.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/forbidden.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/forbidden.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/unauthorized.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/unauthorized.js\", 23));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/app/layout.tsx\"],\n'global-error': [module1, \"next/dist/client/components/builtin/global-error.js\"],\n'not-found': [module2, \"next/dist/client/components/builtin/not-found.js\"],\n'forbidden': [module3, \"next/dist/client/components/builtin/forbidden.js\"],\n'unauthorized': [module4, \"next/dist/client/components/builtin/unauthorized.js\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/代码文件/LyricWritingProject/app/page.tsx\"];\n\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || ''\n});\nasync function handler(req, res, ctx) {\n    var _this;\n    let srcPage = \"/page\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const initialPostponed = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'postponed');\n    // TODO: replace with more specific flags\n    const minimalMode = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'minimalMode');\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, query, params, parsedUrl, pageIsDynamic, buildManifest, nextFontManifest, reactLoadableManifest, serverActionsManifest, clientReferenceManifest, subresourceIntegrityManifest, prerenderManifest, isDraftMode, resolvedPathname, revalidateOnlyGenerated, routerServerContext, nextConfig } = prepareResult;\n    const pathname = parsedUrl.pathname || '/';\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__.normalizeAppPath)(srcPage);\n    let { isOnDemandRevalidate } = prepareResult;\n    const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n    const isPrerendered = prerenderManifest.routes[resolvedPathname];\n    let isSSG = Boolean(prerenderInfo || isPrerendered || prerenderManifest.routes[normalizedSrcPage]);\n    const userAgent = req.headers['user-agent'] || '';\n    const botType = (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.getBotType)(userAgent);\n    const isHtmlBot = (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.isHtmlBotRequest)(req);\n    /**\n   * If true, this indicates that the request being made is for an app\n   * prefetch request.\n   */ const isPrefetchRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isPrefetchRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_ROUTER_PREFETCH_HEADER]);\n    // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n    const isRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.RSC_HEADER]);\n    const isPossibleServerAction = (0,next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__.getIsPossibleServerAction)(req);\n    /**\n   * If the route being rendered is an app page, and the ppr feature has been\n   * enabled, then the given route _could_ support PPR.\n   */ const couldSupportPPR = (0,next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__.checkIsAppPPREnabled)(nextConfig.experimental.ppr);\n    // When enabled, this will allow the use of the `?__nextppronly` query to\n    // enable debugging of the static shell.\n    const hasDebugStaticShellQuery =  false && 0;\n    // When enabled, this will allow the use of the `?__nextppronly` query\n    // to enable debugging of the fallback shell.\n    const hasDebugFallbackShellQuery = hasDebugStaticShellQuery && query.__nextppronly === 'fallback';\n    // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n    // prerender manifest and this is an app page.\n    const isRoutePPREnabled = couldSupportPPR && (((_this = prerenderManifest.routes[normalizedSrcPage] ?? prerenderManifest.dynamicRoutes[normalizedSrcPage]) == null ? void 0 : _this.renderingMode) === 'PARTIALLY_STATIC' || // Ideally we'd want to check the appConfig to see if this page has PPR\n    // enabled or not, but that would require plumbing the appConfig through\n    // to the server during development. We assume that the page supports it\n    // but only during development.\n    hasDebugStaticShellQuery && (routeModule.isDev === true || (routerServerContext == null ? void 0 : routerServerContext.experimentalTestProxy) === true));\n    const isDebugStaticShell = hasDebugStaticShellQuery && isRoutePPREnabled;\n    // We should enable debugging dynamic accesses when the static shell\n    // debugging has been enabled and we're also in development mode.\n    const isDebugDynamicAccesses = isDebugStaticShell && routeModule.isDev === true;\n    const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled;\n    // If we're in minimal mode, then try to get the postponed information from\n    // the request metadata. If available, use it for resuming the postponed\n    // render.\n    const minimalPostponed = isRoutePPREnabled ? initialPostponed : undefined;\n    // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n    // we can use this fact to only generate the flight data for the request\n    // because we can't cache the HTML (as it's also dynamic).\n    const isDynamicRSCRequest = isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest;\n    // Need to read this before it's stripped by stripFlightHeaders. We don't\n    // need to transfer it to the request meta because it's only read\n    // within this function; the static segment data should have already been\n    // generated, so we will always either return a static response or a 404.\n    const segmentPrefetchHeader = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'segmentPrefetchRSCRequest');\n    // TODO: investigate existing bug with shouldServeStreamingMetadata always\n    // being true for a revalidate due to modifying the base-server this.renderOpts\n    // when fixing this to correct logic it causes hydration issue since we set\n    // serveStreamingMetadata to true during export\n    let serveStreamingMetadata = !userAgent ? true : (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.shouldServeStreamingMetadata)(userAgent, nextConfig.htmlLimitedBots);\n    if (isHtmlBot && isRoutePPREnabled) {\n        isSSG = false;\n        serveStreamingMetadata = false;\n    }\n    // In development, we always want to generate dynamic HTML.\n    let supportsDynamicResponse = // If we're in development, we always support dynamic HTML, unless it's\n    // a data request, in which case we only produce static HTML.\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isSSG || // If this request has provided postponed data, it supports dynamic\n    // HTML.\n    typeof initialPostponed === 'string' || // If this is a dynamic RSC request, then this render supports dynamic\n    // HTML (it's dynamic).\n    isDynamicRSCRequest;\n    // When html bots request PPR page, perform the full dynamic rendering.\n    const shouldWaitOnAllReady = isHtmlBot && isRoutePPREnabled;\n    let ssgCacheKey = null;\n    if (!isDraftMode && isSSG && !supportsDynamicResponse && !isPossibleServerAction && !minimalPostponed && !isDynamicRSCRequest) {\n        ssgCacheKey = resolvedPathname;\n    }\n    // the staticPathKey differs from ssgCacheKey since\n    // ssgCacheKey is null in dev since we're always in \"dynamic\"\n    // mode in dev to bypass the cache, but we still need to honor\n    // dynamicParams = false in dev mode\n    let staticPathKey = ssgCacheKey;\n    if (!staticPathKey && routeModule.isDev) {\n        staticPathKey = resolvedPathname;\n    }\n    const ComponentMod = {\n        ...next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__,\n        tree,\n        pages,\n        GlobalError: (next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default()),\n        handler,\n        routeModule,\n        __next_app__\n    };\n    // Before rendering (which initializes component tree modules), we have to\n    // set the reference manifests to our global store so Server Action's\n    // encryption util can access to them at the top level of the page module.\n    if (serverActionsManifest && clientReferenceManifest) {\n        (0,next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__.setReferenceManifestsSingleton)({\n            page: srcPage,\n            clientReferenceManifest,\n            serverActionsManifest,\n            serverModuleMap: (0,next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__.createServerModuleMap)({\n                serverActionsManifest\n            })\n        });\n    }\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    try {\n        const invokeRouteModule = async (span, context)=>{\n            const nextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextRequest(req);\n            const nextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextResponse(res);\n            // TODO: adapt for putting the RDC inside the postponed data\n            // If we're in dev, and this isn't a prefetch or a server action,\n            // we should seed the resume data cache.\n            if (true) {\n                if (nextConfig.experimental.dynamicIO && !isPrefetchRSCRequest && !context.renderOpts.isPossibleServerAction) {\n                    const warmup = await routeModule.warmup(nextReq, nextRes, context);\n                    // If the warmup is successful, we should use the resume data\n                    // cache from the warmup.\n                    if (warmup.metadata.renderResumeDataCache) {\n                        context.renderOpts.renderResumeDataCache = warmup.metadata.renderResumeDataCache;\n                    }\n                }\n            }\n            return routeModule.render(nextReq, nextRes, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const doRender = async ({ span, postponed, fallbackRouteParams })=>{\n            const context = {\n                query,\n                params,\n                page: normalizedSrcPage,\n                sharedContext: {\n                    buildId\n                },\n                serverComponentsHmrCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'serverComponentsHmrCache'),\n                fallbackRouteParams,\n                renderOpts: {\n                    App: ()=>null,\n                    Document: ()=>null,\n                    pageConfig: {},\n                    ComponentMod,\n                    Component: (0,next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__.interopDefault)(ComponentMod),\n                    params,\n                    routeModule,\n                    page: srcPage,\n                    postponed,\n                    shouldWaitOnAllReady,\n                    serveStreamingMetadata,\n                    supportsDynamicResponse: typeof postponed === 'string' || supportsDynamicResponse,\n                    buildManifest,\n                    nextFontManifest,\n                    reactLoadableManifest,\n                    subresourceIntegrityManifest,\n                    serverActionsManifest,\n                    clientReferenceManifest,\n                    setIsrStatus: routerServerContext == null ? void 0 : routerServerContext.setIsrStatus,\n                    dir: routeModule.projectDir,\n                    isDraftMode,\n                    isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,\n                    botType,\n                    isOnDemandRevalidate,\n                    isPossibleServerAction,\n                    assetPrefix: nextConfig.assetPrefix,\n                    nextConfigOutput: nextConfig.output,\n                    crossOrigin: nextConfig.crossOrigin,\n                    trailingSlash: nextConfig.trailingSlash,\n                    previewProps: prerenderManifest.preview,\n                    deploymentId: nextConfig.deploymentId,\n                    enableTainting: nextConfig.experimental.taint,\n                    htmlLimitedBots: nextConfig.htmlLimitedBots,\n                    devtoolSegmentExplorer: nextConfig.experimental.devtoolSegmentExplorer,\n                    reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n                    multiZoneDraftMode,\n                    incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'incrementalCache'),\n                    cacheLifeProfiles: nextConfig.experimental.cacheLife,\n                    basePath: nextConfig.basePath,\n                    serverActions: nextConfig.experimental.serverActions,\n                    ...isDebugStaticShell || isDebugDynamicAccesses ? {\n                        nextExport: true,\n                        supportsDynamicResponse: false,\n                        isStaticGeneration: true,\n                        isRevalidate: true,\n                        isDebugDynamicAccesses: isDebugDynamicAccesses\n                    } : {},\n                    experimental: {\n                        isRoutePPREnabled,\n                        expireTime: nextConfig.expireTime,\n                        staleTimes: nextConfig.experimental.staleTimes,\n                        dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                        clientSegmentCache: Boolean(nextConfig.experimental.clientSegmentCache),\n                        dynamicOnHover: Boolean(nextConfig.experimental.dynamicOnHover),\n                        inlineCss: Boolean(nextConfig.experimental.inlineCss),\n                        authInterrupts: Boolean(nextConfig.experimental.authInterrupts),\n                        clientTraceMetadata: nextConfig.experimental.clientTraceMetadata || []\n                    },\n                    waitUntil: ctx.waitUntil,\n                    onClose: (cb)=>{\n                        res.on('close', cb);\n                    },\n                    onAfterTaskError: ()=>{},\n                    onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext),\n                    err: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'invokeError'),\n                    dev: routeModule.isDev\n                }\n            };\n            const result = await invokeRouteModule(span, context);\n            const { metadata } = result;\n            const { cacheControl, headers = {}, // Add any fetch tags that were on the page to the response headers.\n            fetchTags: cacheTags } = metadata;\n            if (cacheTags) {\n                headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n            }\n            // Pull any fetch metrics from the render onto the request.\n            ;\n            req.fetchMetrics = metadata.fetchMetrics;\n            // we don't throw static to dynamic errors in dev as isSSG\n            // is a best guess in dev since we don't have the prerender pass\n            // to know whether the path is actually static or not\n            if (isSSG && (cacheControl == null ? void 0 : cacheControl.revalidate) === 0 && !routeModule.isDev && !isRoutePPREnabled) {\n                const staticBailoutInfo = metadata.staticBailoutInfo;\n                const err = Object.defineProperty(new Error(`Page changed from static to dynamic at runtime ${resolvedPathname}${(staticBailoutInfo == null ? void 0 : staticBailoutInfo.description) ? `, reason: ${staticBailoutInfo.description}` : ``}` + `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E132\",\n                    enumerable: false,\n                    configurable: true\n                });\n                if (staticBailoutInfo == null ? void 0 : staticBailoutInfo.stack) {\n                    const stack = staticBailoutInfo.stack;\n                    err.stack = err.message + stack.substring(stack.indexOf('\\n'));\n                }\n                throw err;\n            }\n            return {\n                value: {\n                    kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE,\n                    html: result,\n                    headers,\n                    rscData: metadata.flightData,\n                    postponed: metadata.postponed,\n                    status: metadata.statusCode,\n                    segmentData: metadata.segmentData\n                },\n                cacheControl\n            };\n        };\n        const responseGenerator = async ({ hasResolved, previousCacheEntry, isRevalidating, span })=>{\n            const isProduction = routeModule.isDev === false;\n            const didRespond = hasResolved || res.writableEnded;\n            // skip on-demand revalidate if cache is not present and\n            // revalidate-if-generated is set\n            if (isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry && !minimalMode) {\n                if (routerServerContext == null ? void 0 : routerServerContext.render404) {\n                    await routerServerContext.render404(req, res);\n                } else {\n                    res.statusCode = 404;\n                    res.end('This page could not be found');\n                }\n                return null;\n            }\n            let fallbackMode;\n            if (prerenderInfo) {\n                fallbackMode = (0,next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.parseFallbackField)(prerenderInfo.fallback);\n            }\n            // When serving a bot request, we want to serve a blocking render and not\n            // the prerendered page. This ensures that the correct content is served\n            // to the bot in the head.\n            if (fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.PRERENDER && (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.isBot)(userAgent)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if ((previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) === -1) {\n                isOnDemandRevalidate = true;\n            }\n            // TODO: adapt for PPR\n            // only allow on-demand revalidate for fallback: true/blocking\n            // or for prerendered fallback: false paths\n            if (isOnDemandRevalidate && (fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND || previousCacheEntry)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if (!minimalMode && fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER && staticPathKey && !didRespond && !isDraftMode && pageIsDynamic && (isProduction || !isPrerendered)) {\n                // if the page has dynamicParams: false and this pathname wasn't\n                // prerendered trigger the no fallback handling\n                if (// In development, fall through to render to handle missing\n                // getStaticPaths.\n                (isProduction || prerenderInfo) && // When fallback isn't present, abort this render so we 404\n                fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND) {\n                    throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError();\n                }\n                let fallbackResponse;\n                if (isRoutePPREnabled && !isRSCRequest) {\n                    // We use the response cache here to handle the revalidation and\n                    // management of the fallback shell.\n                    fallbackResponse = await routeModule.handleResponse({\n                        cacheKey: isProduction ? normalizedSrcPage : null,\n                        req,\n                        nextConfig,\n                        routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                        isFallback: true,\n                        prerenderManifest,\n                        isRoutePPREnabled,\n                        responseGenerator: async ()=>doRender({\n                                span,\n                                // We pass `undefined` as rendering a fallback isn't resumed\n                                // here.\n                                postponed: undefined,\n                                fallbackRouteParams: // If we're in production or we're debugging the fallback\n                                // shell then we should postpone when dynamic params are\n                                // accessed.\n                                isProduction || isDebugFallbackShell ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(normalizedSrcPage) : null\n                            }),\n                        waitUntil: ctx.waitUntil\n                    });\n                    // If the fallback response was set to null, then we should return null.\n                    if (fallbackResponse === null) return null;\n                    // Otherwise, if we did get a fallback response, we should return it.\n                    if (fallbackResponse) {\n                        // Remove the cache control from the response to prevent it from being\n                        // used in the surrounding cache.\n                        delete fallbackResponse.cacheControl;\n                        return fallbackResponse;\n                    }\n                }\n            }\n            // Only requests that aren't revalidating can be resumed. If we have the\n            // minimal postponed data, then we should resume the render with it.\n            const postponed = !isOnDemandRevalidate && !isRevalidating && minimalPostponed ? minimalPostponed : undefined;\n            // When we're in minimal mode, if we're trying to debug the static shell,\n            // we should just return nothing instead of resuming the dynamic render.\n            if ((isDebugStaticShell || isDebugDynamicAccesses) && typeof postponed !== 'undefined') {\n                return {\n                    cacheControl: {\n                        revalidate: 1,\n                        expire: undefined\n                    },\n                    value: {\n                        kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.PAGES,\n                        html: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                        pageData: {},\n                        headers: undefined,\n                        status: undefined\n                    }\n                };\n            }\n            // If this is a dynamic route with PPR enabled and the default route\n            // matches were set, then we should pass the fallback route params to\n            // the renderer as this is a fallback revalidation request.\n            const fallbackRouteParams = pageIsDynamic && isRoutePPREnabled && ((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'renderFallbackShell') || isDebugFallbackShell) ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(pathname) : null;\n            // Perform the render.\n            return doRender({\n                span,\n                postponed,\n                fallbackRouteParams\n            });\n        };\n        const handleResponse = async (span)=>{\n            var _cacheEntry_value, _cachedData_headers;\n            const cacheEntry = await routeModule.handleResponse({\n                cacheKey: ssgCacheKey,\n                responseGenerator: (c)=>responseGenerator({\n                        span,\n                        ...c\n                    }),\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                isOnDemandRevalidate,\n                isRoutePPREnabled,\n                req,\n                nextConfig,\n                prerenderManifest,\n                waitUntil: ctx.waitUntil\n            });\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            // In dev, we should not cache pages for any reason.\n            if (routeModule.isDev) {\n                res.setHeader('Cache-Control', 'no-store, must-revalidate');\n            }\n            if (!cacheEntry) {\n                if (ssgCacheKey) {\n                    // A cache entry might not be generated if a response is written\n                    // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n                    // have a cache key. If we do have a cache key but we don't end up\n                    // with a cache entry, then either Next.js or the application has a\n                    // bug that needs fixing.\n                    throw Object.defineProperty(new Error('invariant: cache entry required but not generated'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E62\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                return null;\n            }\n            if (((_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant app-page handler received invalid cache entry ${(_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E707\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            const didPostpone = typeof cacheEntry.value.postponed === 'string';\n            if (isSSG && // We don't want to send a cache header for requests that contain dynamic\n            // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n            // request, then we should set the cache header.\n            !isDynamicRSCRequest && (!didPostpone || isPrefetchRSCRequest)) {\n                if (!minimalMode) {\n                    // set x-nextjs-cache header to match the header\n                    // we set for the image-optimizer\n                    res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n                }\n                // Set a header used by the client router to signal the response is static\n                // and should respect the `static` cache staleTime value.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_IS_PRERENDER_HEADER, '1');\n            }\n            const { value: cachedData } = cacheEntry;\n            // Coerce the cache control parameter from the render.\n            let cacheControl;\n            // If this is a resume request in minimal mode it is streamed with dynamic\n            // content and should not be cached.\n            if (minimalPostponed) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (minimalMode && isRSCRequest && !isPrefetchRSCRequest && isRoutePPREnabled) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (!routeModule.isDev) {\n                // If this is a preview mode request, we shouldn't cache it\n                if (isDraftMode) {\n                    cacheControl = {\n                        revalidate: 0,\n                        expire: undefined\n                    };\n                } else if (!isSSG) {\n                    if (!res.getHeader('Cache-Control')) {\n                        cacheControl = {\n                            revalidate: 0,\n                            expire: undefined\n                        };\n                    }\n                } else if (cacheEntry.cacheControl) {\n                    // If the cache entry has a cache control with a revalidate value that's\n                    // a number, use it.\n                    if (typeof cacheEntry.cacheControl.revalidate === 'number') {\n                        var _cacheEntry_cacheControl;\n                        if (cacheEntry.cacheControl.revalidate < 1) {\n                            throw Object.defineProperty(new Error(`Invalid revalidate configuration provided: ${cacheEntry.cacheControl.revalidate} < 1`), \"__NEXT_ERROR_CODE\", {\n                                value: \"E22\",\n                                enumerable: false,\n                                configurable: true\n                            });\n                        }\n                        cacheControl = {\n                            revalidate: cacheEntry.cacheControl.revalidate,\n                            expire: ((_cacheEntry_cacheControl = cacheEntry.cacheControl) == null ? void 0 : _cacheEntry_cacheControl.expire) ?? nextConfig.expireTime\n                        };\n                    } else {\n                        cacheControl = {\n                            revalidate: next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.CACHE_ONE_YEAR,\n                            expire: undefined\n                        };\n                    }\n                }\n            }\n            cacheEntry.cacheControl = cacheControl;\n            if (typeof segmentPrefetchHeader === 'string' && (cachedData == null ? void 0 : cachedData.kind) === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE && cachedData.segmentData) {\n                var _cachedData_headers1;\n                // This is a prefetch request issued by the client Segment Cache. These\n                // should never reach the application layer (lambda). We should either\n                // respond from the cache (HIT) or respond with 204 No Content (MISS).\n                // Set a header to indicate that PPR is enabled for this route. This\n                // lets the client distinguish between a regular cache miss and a cache\n                // miss due to PPR being disabled. In other contexts this header is used\n                // to indicate that the response contains dynamic data, but here we're\n                // only using it to indicate that the feature is enabled — the segment\n                // response itself contains whether the data is dynamic.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '2');\n                // Add the cache tags header to the response if it exists and we're in\n                // minimal mode while rendering a static page.\n                const tags = (_cachedData_headers1 = cachedData.headers) == null ? void 0 : _cachedData_headers1[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                    res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n                }\n                const matchedSegment = cachedData.segmentData.get(segmentPrefetchHeader);\n                if (matchedSegment !== undefined) {\n                    // Cache hit\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(matchedSegment),\n                        cacheControl: cacheEntry.cacheControl\n                    });\n                }\n                // Cache miss. Either a cache entry for this route has not been generated\n                // (which technically should not be possible when PPR is enabled, because\n                // at a minimum there should always be a fallback entry) or there's no\n                // match for the requested segment. Respond with a 204 No Content. We\n                // don't bother to respond with 404, because these requests are only\n                // issued as part of a prefetch.\n                res.statusCode = 204;\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If there's a callback for `onCacheEntry`, call it with the cache entry\n            // and the revalidate options.\n            const onCacheEntry = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'onCacheEntry');\n            if (onCacheEntry) {\n                const finished = await onCacheEntry({\n                    ...cacheEntry,\n                    // TODO: remove this when upstream doesn't\n                    // always expect this value to be \"PAGE\"\n                    value: {\n                        ...cacheEntry.value,\n                        kind: 'PAGE'\n                    }\n                }, {\n                    url: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'initURL')\n                });\n                if (finished) {\n                    // TODO: maybe we have to end the request?\n                    return null;\n                }\n            }\n            // If the request has a postponed state and it's a resume request we\n            // should error.\n            if (didPostpone && minimalPostponed) {\n                throw Object.defineProperty(new Error('Invariant: postponed state should not be present on a resume request'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E396\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (cachedData.headers) {\n                const headers = {\n                    ...cachedData.headers\n                };\n                if (!minimalMode || !isSSG) {\n                    delete headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                }\n                for (let [key, value] of Object.entries(headers)){\n                    if (typeof value === 'undefined') continue;\n                    if (Array.isArray(value)) {\n                        for (const v of value){\n                            res.appendHeader(key, v);\n                        }\n                    } else if (typeof value === 'number') {\n                        value = value.toString();\n                        res.appendHeader(key, value);\n                    } else {\n                        res.appendHeader(key, value);\n                    }\n                }\n            }\n            // Add the cache tags header to the response if it exists and we're in\n            // minimal mode while rendering a static page.\n            const tags = (_cachedData_headers = cachedData.headers) == null ? void 0 : _cachedData_headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n            if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n            }\n            // If the request is a data request, then we shouldn't set the status code\n            // from the response because it should always be 200. This should be gated\n            // behind the experimental PPR flag.\n            if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n                res.statusCode = cachedData.status;\n            }\n            // Redirect information is encoded in RSC payload, so we don't need to use redirect status codes\n            if (!minimalMode && cachedData.status && next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__.RedirectStatusCode[cachedData.status] && isRSCRequest) {\n                res.statusCode = 200;\n            }\n            // Mark that the request did postpone.\n            if (didPostpone) {\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '1');\n            }\n            // we don't go through this block when preview mode is true\n            // as preview mode is a dynamic request (bypasses cache) and doesn't\n            // generate both HTML and payloads in the same request so continue to just\n            // return the generated payload\n            if (isRSCRequest && !isDraftMode) {\n                // If this is a dynamic RSC request, then stream the response.\n                if (typeof cachedData.rscData === 'undefined') {\n                    if (cachedData.postponed) {\n                        throw Object.defineProperty(new Error('Invariant: Expected postponed to be undefined'), \"__NEXT_ERROR_CODE\", {\n                            value: \"E372\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    }\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: cachedData.html,\n                        // Dynamic RSC responses cannot be cached, even if they're\n                        // configured with `force-static` because we have no way of\n                        // distinguishing between `force-static` and pages that have no\n                        // postponed state.\n                        // TODO: distinguish `force-static` from pages with no postponed state (static)\n                        cacheControl: isDynamicRSCRequest ? {\n                            revalidate: 0,\n                            expire: undefined\n                        } : cacheEntry.cacheControl\n                    });\n                }\n                // As this isn't a prefetch request, we should serve the static flight\n                // data.\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(cachedData.rscData),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // This is a request for HTML data.\n            let body = cachedData.html;\n            // If there's no postponed state, we should just serve the HTML. This\n            // should also be the case for a resume request because it's completed\n            // as a server render (rather than a static render).\n            if (!didPostpone || minimalMode) {\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If we're debugging the static shell or the dynamic API accesses, we\n            // should just serve the HTML without resuming the render. The returned\n            // HTML will be the static shell so all the Dynamic API's will be used\n            // during static generation.\n            if (isDebugStaticShell || isDebugDynamicAccesses) {\n                // Since we're not resuming the render, we need to at least add the\n                // closing body and html tags to create valid HTML.\n                body.chain(new ReadableStream({\n                    start (controller) {\n                        controller.enqueue(next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__.ENCODED_TAGS.CLOSED.BODY_AND_HTML);\n                        controller.close();\n                    }\n                }));\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: {\n                        revalidate: 0,\n                        expire: undefined\n                    }\n                });\n            }\n            // This request has postponed, so let's create a new transformer that the\n            // dynamic data can pipe to that will attach the dynamic data to the end\n            // of the response.\n            const transformer = new TransformStream();\n            body.chain(transformer.readable);\n            // Perform the render again, but this time, provide the postponed state.\n            // We don't await because we want the result to start streaming now, and\n            // we've already chained the transformer's readable to the render result.\n            doRender({\n                span,\n                postponed: cachedData.postponed,\n                // This is a resume render, not a fallback render, so we don't need to\n                // set this.\n                fallbackRouteParams: null\n            }).then(async (result)=>{\n                var _result_value;\n                if (!result) {\n                    throw Object.defineProperty(new Error('Invariant: expected a result to be returned'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E463\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                if (((_result_value = result.value) == null ? void 0 : _result_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                    var _result_value1;\n                    throw Object.defineProperty(new Error(`Invariant: expected a page response, got ${(_result_value1 = result.value) == null ? void 0 : _result_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                        value: \"E305\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                // Pipe the resume result to the transformer.\n                await result.value.html.pipeTo(transformer.writable);\n            }).catch((err)=>{\n                // An error occurred during piping or preparing the render, abort\n                // the transformers writer so we can terminate the stream.\n                transformer.writable.abort(err).catch((e)=>{\n                    console.error(\"couldn't abort transformer\", e);\n                });\n            });\n            return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                req,\n                res,\n                type: 'html',\n                generateEtags: nextConfig.generateEtags,\n                poweredByHeader: nextConfig.poweredByHeader,\n                result: body,\n                // We don't want to cache the response if it has postponed data because\n                // the response being sent to the client it's dynamic parts are streamed\n                // to the client on the same request.\n                cacheControl: {\n                    revalidate: 0,\n                    expire: undefined\n                }\n            });\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            return await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: srcPage,\n                routeType: 'render',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__.getRevalidateReason)({\n                    isRevalidate: isSSG,\n                    isOnDemandRevalidate\n                })\n            }, routerServerContext);\n        }\n        // rethrow so that we can handle serving error page\n        throw err;\n    }\n}\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Ffrontend%2Fcomponets%2Fcomponet%2Fmain_container.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Ffrontend%2Fcomponets%2Fcomponet%2Fmain_container.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./frontend/componets/componet/main_container.tsx */ \"(rsc)/./frontend/componets/componet/main_container.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGemhhb196aV9mZW5nJTJGRGVza3RvcCUyRiVFNCVCQiVBMyVFNyVBMCU4MSVFNiU5NiU4NyVFNCVCQiVCNiUyRkx5cmljV3JpdGluZ1Byb2plY3QlMkZmcm9udGVuZCUyRmNvbXBvbmV0cyUyRmNvbXBvbmV0JTJGbWFpbl9jb250YWluZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNE1BQXFLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiL1VzZXJzL3poYW9femlfZmVuZy9EZXNrdG9wL+S7o+eggeaWh+S7ti9MeXJpY1dyaXRpbmdQcm9qZWN0L2Zyb250ZW5kL2NvbXBvbmV0cy9jb21wb25ldC9tYWluX2NvbnRhaW5lci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Ffrontend%2Fcomponets%2Fcomponet%2Fmain_container.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(rsc)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGemhhb196aV9mZW5nJTJGRGVza3RvcCUyRiVFNCVCQiVBMyVFNyVBMCU4MSVFNiU5NiU4NyVFNCVCQiVCNiUyRkx5cmljV3JpdGluZ1Byb2plY3QlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZidWlsdGluJTJGZ2xvYmFsLWVycm9yLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGemhhb196aV9mZW5nJTJGRGVza3RvcCUyRiVFNCVCQiVBMyVFNyVBMCU4MSVFNiU5NiU4NyVFNCVCQiVCNiUyRkx5cmljV3JpdGluZ1Byb2plY3QlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRnpoYW9femlfZmVuZyUyRkRlc2t0b3AlMkYlRTQlQkIlQTMlRTclQTAlODElRTYlOTYlODclRTQlQkIlQjYlMkZMeXJpY1dyaXRpbmdQcm9qZWN0JTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGY2xpZW50LXNlZ21lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZ6aGFvX3ppX2ZlbmclMkZEZXNrdG9wJTJGJUU0JUJCJUEzJUU3JUEwJTgxJUU2JTk2JTg3JUU0JUJCJUI2JTJGTHlyaWNXcml0aW5nUHJvamVjdCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmh0dHAtYWNjZXNzLWZhbGxiYWNrJTJGZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZ6aGFvX3ppX2ZlbmclMkZEZXNrdG9wJTJGJUU0JUJCJUEzJUU3JUEwJTgxJUU2JTk2JTg3JUU0JUJCJUI2JTJGTHlyaWNXcml0aW5nUHJvamVjdCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmxheW91dC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZ6aGFvX3ppX2ZlbmclMkZEZXNrdG9wJTJGJUU0JUJCJUEzJUU3JUEwJTgxJUU2JTk2JTg3JUU0JUJCJUI2JTJGTHlyaWNXcml0aW5nUHJvamVjdCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRm1ldGFkYXRhJTJGYXN5bmMtbWV0YWRhdGEuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZ6aGFvX3ppX2ZlbmclMkZEZXNrdG9wJTJGJUU0JUJCJUEzJUU3JUEwJTgxJUU2JTk2JTg3JUU0JUJCJUI2JTJGTHlyaWNXcml0aW5nUHJvamVjdCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRm1ldGFkYXRhJTJGbWV0YWRhdGEtYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZ6aGFvX3ppX2ZlbmclMkZEZXNrdG9wJTJGJUU0JUJCJUEzJUU3JUEwJTgxJUU2JTk2JTg3JUU0JUJCJUI2JTJGTHlyaWNXcml0aW5nUHJvamVjdCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRnJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZ6aGFvX3ppX2ZlbmclMkZEZXNrdG9wJTJGJUU0JUJCJUEzJUU3JUEwJTgxJUU2JTk2JTg3JUU0JUJCJUI2JTJGTHlyaWNXcml0aW5nUHJvamVjdCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGbGliJTJGbWV0YWRhdGElMkZnZW5lcmF0ZSUyRmljb24tbWFyay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRnpoYW9femlfZmVuZyUyRkRlc2t0b3AlMkYlRTQlQkIlQTMlRTclQTAlODElRTYlOTYlODclRTQlQkIlQjYlMkZMeXJpY1dyaXRpbmdQcm9qZWN0JTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZuZXh0LWRldnRvb2xzJTJGdXNlcnNwYWNlJTJGYXBwJTJGc2VnbWVudC1leHBsb3Jlci1ub2RlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzUEFBMEo7QUFDMUo7QUFDQSxvT0FBaUo7QUFDako7QUFDQSwwT0FBb0o7QUFDcEo7QUFDQSxvUkFBeUs7QUFDeks7QUFDQSx3T0FBbUo7QUFDbko7QUFDQSw0UEFBNko7QUFDN0o7QUFDQSxrUUFBZ0s7QUFDaEs7QUFDQSxzUUFBa0s7QUFDbEs7QUFDQSx3T0FBbUo7QUFDbko7QUFDQSw0UUFBcUsiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy96aGFvX3ppX2ZlbmcvRGVza3RvcC/ku6PnoIHmlofku7YvTHlyaWNXcml0aW5nUHJvamVjdC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2J1aWx0aW4vZ2xvYmFsLWVycm9yLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvemhhb196aV9mZW5nL0Rlc2t0b3Av5Luj56CB5paH5Lu2L0x5cmljV3JpdGluZ1Byb2plY3Qvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9jbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3poYW9femlfZmVuZy9EZXNrdG9wL+S7o+eggeaWh+S7ti9MeXJpY1dyaXRpbmdQcm9qZWN0L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvY2xpZW50LXNlZ21lbnQuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy96aGFvX3ppX2ZlbmcvRGVza3RvcC/ku6PnoIHmlofku7YvTHlyaWNXcml0aW5nUHJvamVjdC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2h0dHAtYWNjZXNzLWZhbGxiYWNrL2Vycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvemhhb196aV9mZW5nL0Rlc2t0b3Av5Luj56CB5paH5Lu2L0x5cmljV3JpdGluZ1Byb2plY3Qvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9sYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvemhhb196aV9mZW5nL0Rlc2t0b3Av5Luj56CB5paH5Lu2L0x5cmljV3JpdGluZ1Byb2plY3Qvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9tZXRhZGF0YS9hc3luYy1tZXRhZGF0YS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3poYW9femlfZmVuZy9EZXNrdG9wL+S7o+eggeaWh+S7ti9MeXJpY1dyaXRpbmdQcm9qZWN0L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbWV0YWRhdGEvbWV0YWRhdGEtYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy96aGFvX3ppX2ZlbmcvRGVza3RvcC/ku6PnoIHmlofku7YvTHlyaWNXcml0aW5nUHJvamVjdC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy96aGFvX3ppX2ZlbmcvRGVza3RvcC/ku6PnoIHmlofku7YvTHlyaWNXcml0aW5nUHJvamVjdC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZW5lcmF0ZS9pY29uLW1hcmsuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy96aGFvX3ppX2ZlbmcvRGVza3RvcC/ku6PnoIHmlofku7YvTHlyaWNXcml0aW5nUHJvamVjdC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L25leHQtZGV2dG9vbHMvdXNlcnNwYWNlL2FwcC9zZWdtZW50LWV4cGxvcmVyLW5vZGUuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./frontend/ButtonCodes/index.ts":
/*!***************************************!*\
  !*** ./frontend/ButtonCodes/index.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_BUTTON_TEXT: () => (/* reexport safe */ _secondary_style_style_secondary__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_BUTTON_TEXT),\n/* harmony export */   SecondaryButton: () => (/* reexport safe */ _secondary_SecondaryButton_SecondaryButton__WEBPACK_IMPORTED_MODULE_0__.SecondaryButton),\n/* harmony export */   SecondaryButtonEventHandler: () => (/* reexport safe */ _secondary_event_event_secondary__WEBPACK_IMPORTED_MODULE_2__.SecondaryButtonEventHandler),\n/* harmony export */   createSecondaryButtonEventHandler: () => (/* reexport safe */ _secondary_event_event_secondary__WEBPACK_IMPORTED_MODULE_2__.createSecondaryButtonEventHandler),\n/* harmony export */   secondaryButtonStyles: () => (/* reexport safe */ _secondary_style_style_secondary__WEBPACK_IMPORTED_MODULE_1__.secondaryButtonStyles)\n/* harmony export */ });\n/* harmony import */ var _secondary_SecondaryButton_SecondaryButton__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./secondary/SecondaryButton/SecondaryButton */ \"(ssr)/./frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx\");\n/* harmony import */ var _secondary_style_style_secondary__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./secondary/style/style_secondary */ \"(ssr)/./frontend/ButtonCodes/secondary/style/style_secondary.ts\");\n/* harmony import */ var _secondary_event_event_secondary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./secondary/event/event_secondary */ \"(ssr)/./frontend/ButtonCodes/secondary/event/event_secondary.ts\");\n// 按键组件统一导出入口\n// 导出普通按键组件\n\n// 导出普通按键样式\n\n// 导出普通按键事件\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9mcm9udGVuZC9CdXR0b25Db2Rlcy9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFBLGFBQWE7QUFFYixXQUFXO0FBQ21FO0FBRzlFLFdBQVc7QUFJZ0M7QUFHM0MsV0FBVztBQUlnQyIsInNvdXJjZXMiOlsiL1VzZXJzL3poYW9femlfZmVuZy9EZXNrdG9wL+S7o+eggeaWh+S7ti9MeXJpY1dyaXRpbmdQcm9qZWN0L2Zyb250ZW5kL0J1dHRvbkNvZGVzL2luZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIOaMiemUrue7hOS7tue7n+S4gOWvvOWHuuWFpeWPo1xuXG4vLyDlr7zlh7rmma7pgJrmjInplK7nu4Tku7ZcbmV4cG9ydCB7IFNlY29uZGFyeUJ1dHRvbiB9IGZyb20gJy4vc2Vjb25kYXJ5L1NlY29uZGFyeUJ1dHRvbi9TZWNvbmRhcnlCdXR0b24nO1xuZXhwb3J0IHR5cGUgeyBTZWNvbmRhcnlCdXR0b25Qcm9wcyB9IGZyb20gJy4vc2Vjb25kYXJ5L1NlY29uZGFyeUJ1dHRvbi9TZWNvbmRhcnlCdXR0b24nO1xuXG4vLyDlr7zlh7rmma7pgJrmjInplK7moLflvI9cbmV4cG9ydCB7IFxuICBzZWNvbmRhcnlCdXR0b25TdHlsZXMsIFxuICBERUZBVUxUX0JVVFRPTl9URVhUIFxufSBmcm9tICcuL3NlY29uZGFyeS9zdHlsZS9zdHlsZV9zZWNvbmRhcnknO1xuZXhwb3J0IHR5cGUgeyBTZWNvbmRhcnlCdXR0b25TdHlsZXMgfSBmcm9tICcuL3NlY29uZGFyeS9zdHlsZS9zdHlsZV9zZWNvbmRhcnknO1xuXG4vLyDlr7zlh7rmma7pgJrmjInplK7kuovku7ZcbmV4cG9ydCB7IFxuICBTZWNvbmRhcnlCdXR0b25FdmVudEhhbmRsZXIsXG4gIGNyZWF0ZVNlY29uZGFyeUJ1dHRvbkV2ZW50SGFuZGxlciBcbn0gZnJvbSAnLi9zZWNvbmRhcnkvZXZlbnQvZXZlbnRfc2Vjb25kYXJ5JztcbmV4cG9ydCB0eXBlIHsgXG4gIEJ1dHRvbk1vZGUsIFxuICBTZWNvbmRhcnlCdXR0b25TdGF0ZSxcbiAgU2Vjb25kYXJ5QnV0dG9uRXZlbnRzIFxufSBmcm9tICcuL3NlY29uZGFyeS9ldmVudC9ldmVudF9zZWNvbmRhcnknO1xuIl0sIm5hbWVzIjpbIlNlY29uZGFyeUJ1dHRvbiIsInNlY29uZGFyeUJ1dHRvblN0eWxlcyIsIkRFRkFVTFRfQlVUVE9OX1RFWFQiLCJTZWNvbmRhcnlCdXR0b25FdmVudEhhbmRsZXIiLCJjcmVhdGVTZWNvbmRhcnlCdXR0b25FdmVudEhhbmRsZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./frontend/ButtonCodes/index.ts\n");

/***/ }),

/***/ "(ssr)/./frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx":
/*!****************************************************************************!*\
  !*** ./frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SecondaryButton: () => (/* binding */ SecondaryButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../style/style_secondary */ \"(ssr)/./frontend/ButtonCodes/secondary/style/style_secondary.ts\");\n/* __next_internal_client_entry_do_not_use__ SecondaryButton auto */ \n\n\n// 普通按键组件\nconst SecondaryButton = ({ mode = 'toggle', initialActive = false, text = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_BUTTON_TEXT, onClick, onMouseEnter, onMouseLeave, onMouseDown, onMouseUp, customStyles = {}, disabled = false })=>{\n    // 内部状态管理\n    const [isActive, setIsActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialActive);\n    const [isHovered, setIsHovered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPressed, setIsPressed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 同步外部 initialActive 属性的变化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SecondaryButton.useEffect\": ()=>{\n            setIsActive(initialActive);\n        }\n    }[\"SecondaryButton.useEffect\"], [\n        initialActive\n    ]);\n    // 计算当前样式\n    const currentStyles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SecondaryButton.useMemo[currentStyles]\": ()=>{\n            const baseStyles = {\n                ..._style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.default\n            };\n            let backgroundColor = baseStyles.backgroundColor;\n            if (mode === 'toggle') {\n                // 持续状态模式\n                if (isActive) {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.active.backgroundColor;\n                    if (isHovered && !disabled) {\n                        backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.active.hover;\n                    }\n                } else {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.inactive.backgroundColor;\n                    if (isHovered && !disabled) {\n                        backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.toggle.inactive.hover;\n                    }\n                }\n            } else if (mode === 'instant') {\n                // 瞬时状态模式\n                if (isPressed && !disabled) {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.instant.active;\n                } else if (isHovered && !disabled) {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.instant.hover;\n                } else {\n                    backgroundColor = _style_style_secondary__WEBPACK_IMPORTED_MODULE_2__.secondaryButtonStyles.instant.default;\n                }\n            }\n            // 应用禁用状态的样式修改\n            const finalStyles = {\n                ...baseStyles,\n                ...customStyles,\n                backgroundColor\n            };\n            if (disabled) {\n                finalStyles.cursor = 'not-allowed';\n                finalStyles.opacity = 0.8; // 稍微降低透明度但仍能看清激活颜色\n            }\n            return finalStyles;\n        }\n    }[\"SecondaryButton.useMemo[currentStyles]\"], [\n        mode,\n        isActive,\n        isHovered,\n        isPressed,\n        disabled,\n        customStyles\n    ]);\n    // 事件处理函数\n    const handleClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleClick]\": ()=>{\n            if (disabled) return;\n            if (mode === 'toggle') {\n                // 直接在组件中处理toggle逻辑\n                const newActiveState = !isActive;\n                setIsActive(newActiveState);\n                onClick?.(newActiveState);\n            } else if (mode === 'instant') {\n                // instant模式\n                setIsActive(true);\n                onClick?.(true);\n            }\n        }\n    }[\"SecondaryButton.useCallback[handleClick]\"], [\n        disabled,\n        mode,\n        isActive,\n        onClick\n    ]);\n    const handleMouseEnter = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseEnter]\": ()=>{\n            if (disabled) return;\n            setIsHovered(true);\n            onMouseEnter?.();\n        }\n    }[\"SecondaryButton.useCallback[handleMouseEnter]\"], [\n        disabled,\n        onMouseEnter\n    ]);\n    const handleMouseLeave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseLeave]\": ()=>{\n            if (disabled) return;\n            setIsHovered(false);\n            setIsPressed(false);\n            onMouseLeave?.();\n        }\n    }[\"SecondaryButton.useCallback[handleMouseLeave]\"], [\n        disabled,\n        onMouseLeave\n    ]);\n    const handleMouseDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseDown]\": ()=>{\n            if (disabled) return;\n            setIsPressed(true);\n            if (mode === 'instant') {\n                setIsActive(true);\n            }\n            onMouseDown?.();\n        }\n    }[\"SecondaryButton.useCallback[handleMouseDown]\"], [\n        disabled,\n        mode,\n        onMouseDown\n    ]);\n    const handleMouseUp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SecondaryButton.useCallback[handleMouseUp]\": ()=>{\n            if (disabled) return;\n            setIsPressed(false);\n            if (mode === 'instant') {\n                setIsActive(false);\n            }\n            onMouseUp?.();\n        }\n    }[\"SecondaryButton.useCallback[handleMouseUp]\"], [\n        disabled,\n        mode,\n        onMouseUp\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        style: currentStyles,\n        onClick: handleClick,\n        onMouseEnter: handleMouseEnter,\n        onMouseLeave: handleMouseLeave,\n        onMouseDown: handleMouseDown,\n        onMouseUp: handleMouseUp,\n        disabled: disabled,\n        type: \"button\",\n        children: text\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx\",\n        lineNumber: 151,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx\n");

/***/ }),

/***/ "(ssr)/./frontend/ButtonCodes/secondary/event/event_secondary.ts":
/*!*****************************************************************!*\
  !*** ./frontend/ButtonCodes/secondary/event/event_secondary.ts ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SecondaryButtonEventHandler: () => (/* binding */ SecondaryButtonEventHandler),\n/* harmony export */   createSecondaryButtonEventHandler: () => (/* binding */ createSecondaryButtonEventHandler)\n/* harmony export */ });\n// 普通按键事件类型定义\n// 普通按键事件处理类\nclass SecondaryButtonEventHandler {\n    constructor(initialState = {\n        isActive: false,\n        mode: 'toggle'\n    }, events = {}){\n        this.state = initialState;\n        this.events = events;\n    }\n    // 获取当前状态\n    getState() {\n        return {\n            ...this.state\n        };\n    }\n    // 设置模式\n    setMode(mode) {\n        this.state.mode = mode;\n    }\n    // 设置激活状态\n    setActive(isActive) {\n        this.state.isActive = isActive;\n    }\n    // 处理点击事件\n    handleClick() {\n        if (this.state.mode === 'toggle') {\n            // 持续状态模式：切换并保持状态\n            this.state.isActive = !this.state.isActive;\n        } else if (this.state.mode === 'instant') {\n            // 瞬时状态模式：瞬间激活\n            this.state.isActive = true;\n        }\n        // 调用外部点击事件\n        if (this.events.onClick) {\n            this.events.onClick(this.state.isActive);\n        }\n        return this.state.isActive;\n    }\n    // 处理鼠标进入事件\n    handleMouseEnter() {\n        if (this.events.onMouseEnter) {\n            this.events.onMouseEnter();\n        }\n    }\n    // 处理鼠标离开事件\n    handleMouseLeave() {\n        if (this.events.onMouseLeave) {\n            this.events.onMouseLeave();\n        }\n    }\n    // 处理鼠标按下事件\n    handleMouseDown() {\n        if (this.state.mode === 'instant') {\n            this.state.isActive = true;\n        }\n        if (this.events.onMouseDown) {\n            this.events.onMouseDown();\n        }\n    }\n    // 处理鼠标松开事件\n    handleMouseUp() {\n        if (this.state.mode === 'instant') {\n            this.state.isActive = false;\n        }\n        if (this.events.onMouseUp) {\n            this.events.onMouseUp();\n        }\n    }\n    // 更新事件处理器\n    updateEvents(events) {\n        this.events = {\n            ...this.events,\n            ...events\n        };\n    }\n}\n// 默认事件处理器工厂函数\nconst createSecondaryButtonEventHandler = (mode = 'toggle', events = {}, initialActive = false)=>{\n    return new SecondaryButtonEventHandler({\n        isActive: initialActive,\n        mode\n    }, events);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./frontend/ButtonCodes/secondary/event/event_secondary.ts\n");

/***/ }),

/***/ "(ssr)/./frontend/ButtonCodes/secondary/style/style_secondary.ts":
/*!*****************************************************************!*\
  !*** ./frontend/ButtonCodes/secondary/style/style_secondary.ts ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_BUTTON_TEXT: () => (/* binding */ DEFAULT_BUTTON_TEXT),\n/* harmony export */   secondaryButtonStyleConfig: () => (/* binding */ secondaryButtonStyleConfig),\n/* harmony export */   secondaryButtonStyles: () => (/* binding */ secondaryButtonStyles)\n/* harmony export */ });\n// 普通按键样式配置\nconst secondaryButtonStyles = {\n    // 默认样式\n    default: {\n        width: '200px',\n        height: '50px',\n        borderRadius: '0px',\n        backgroundColor: '#f1f1f1',\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        cursor: 'pointer',\n        fontSize: '20px',\n        color: '#242424',\n        textAlign: 'center',\n        lineHeight: 1.5,\n        border: 'none',\n        outline: 'none',\n        transition: 'background-color 0.2s ease'\n    },\n    // 持续状态模式样式\n    toggle: {\n        active: {\n            backgroundColor: '#929292',\n            hover: '#858585'\n        },\n        inactive: {\n            backgroundColor: '#f1f1f1',\n            hover: '#e4e4e4'\n        }\n    },\n    // 瞬时状态模式样式\n    instant: {\n        hover: '#e4e4e4',\n        active: '#858585',\n        default: '#f1f1f1'\n    }\n};\n// 默认文本\nconst DEFAULT_BUTTON_TEXT = '按键';\n// 普通按键样式配置\nconst secondaryButtonStyleConfig = {\n    styles: secondaryButtonStyles,\n    defaultText: DEFAULT_BUTTON_TEXT\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./frontend/ButtonCodes/secondary/style/style_secondary.ts\n");

/***/ }),

/***/ "(ssr)/./frontend/Store/store.ts":
/*!*********************************!*\
  !*** ./frontend/Store/store.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useContainerStore: () => (/* binding */ useContainerStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n\nconst useContainerStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set)=>({\n        activeContainer: 'mode',\n        setActiveContainer: (container)=>set({\n                activeContainer: container\n            })\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9mcm9udGVuZC9TdG9yZS9zdG9yZS50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFnQztBQU96QixNQUFNQyxvQkFBb0JELCtDQUFNQSxDQUFpQixDQUFDRSxNQUFTO1FBQ2hFQyxpQkFBaUI7UUFDakJDLG9CQUFvQixDQUFDQyxZQUFjSCxJQUFJO2dCQUFFQyxpQkFBaUJFO1lBQVU7SUFDdEUsSUFBRyIsInNvdXJjZXMiOlsiL1VzZXJzL3poYW9femlfZmVuZy9EZXNrdG9wL+S7o+eggeaWh+S7ti9MeXJpY1dyaXRpbmdQcm9qZWN0L2Zyb250ZW5kL1N0b3JlL3N0b3JlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZSB9IGZyb20gJ3p1c3RhbmQnXG5cbmludGVyZmFjZSBDb250YWluZXJTdGF0ZSB7XG4gIGFjdGl2ZUNvbnRhaW5lcjogJ21vZGUnIHwgJ2J1c2luZXNzJ1xuICBzZXRBY3RpdmVDb250YWluZXI6IChjb250YWluZXI6ICdtb2RlJyB8ICdidXNpbmVzcycpID0+IHZvaWRcbn1cblxuZXhwb3J0IGNvbnN0IHVzZUNvbnRhaW5lclN0b3JlID0gY3JlYXRlPENvbnRhaW5lclN0YXRlPigoc2V0KSA9PiAoe1xuICBhY3RpdmVDb250YWluZXI6ICdtb2RlJywgLy8g6buY6K6k5pi+56S65qih5byP5a655ZmoXG4gIHNldEFjdGl2ZUNvbnRhaW5lcjogKGNvbnRhaW5lcikgPT4gc2V0KHsgYWN0aXZlQ29udGFpbmVyOiBjb250YWluZXIgfSlcbn0pKVxuIl0sIm5hbWVzIjpbImNyZWF0ZSIsInVzZUNvbnRhaW5lclN0b3JlIiwic2V0IiwiYWN0aXZlQ29udGFpbmVyIiwic2V0QWN0aXZlQ29udGFpbmVyIiwiY29udGFpbmVyIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./frontend/Store/store.ts\n");

/***/ }),

/***/ "(ssr)/./frontend/componets/componet/componetA1.tsx":
/*!****************************************************!*\
  !*** ./frontend/componets/componet/componetA1.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst ComponetA1 = ()=>{\n    const containerStyle = {\n        height: '95vh',\n        width: '95vh',\n        backgroundColor: '#6d6d6d',\n        display: 'flex',\n        flexDirection: 'column',\n        justifyContent: 'center',\n        alignItems: 'center',\n        overflow: 'hidden'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: containerStyle\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/componet/componetA1.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ComponetA1);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9mcm9udGVuZC9jb21wb25ldHMvY29tcG9uZXQvY29tcG9uZXRBMS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRXlCO0FBRXpCLE1BQU1DLGFBQXVCO0lBQzNCLE1BQU1DLGlCQUFzQztRQUMxQ0MsUUFBUTtRQUNSQyxPQUFPO1FBQ1BDLGlCQUFpQjtRQUNqQkMsU0FBUztRQUNUQyxlQUFlO1FBQ2ZDLGdCQUFnQjtRQUNoQkMsWUFBWTtRQUNaQyxVQUFVO0lBQ1o7SUFFQSxxQkFDRSw4REFBQ0M7UUFBSUMsT0FBT1Y7Ozs7OztBQUloQjtBQUVBLGlFQUFlRCxVQUFVQSxFQUFBIiwic291cmNlcyI6WyIvVXNlcnMvemhhb196aV9mZW5nL0Rlc2t0b3Av5Luj56CB5paH5Lu2L0x5cmljV3JpdGluZ1Byb2plY3QvZnJvbnRlbmQvY29tcG9uZXRzL2NvbXBvbmV0L2NvbXBvbmV0QTEudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXG5cbmNvbnN0IENvbXBvbmV0QTE6IFJlYWN0LkZDID0gKCkgPT4ge1xuICBjb25zdCBjb250YWluZXJTdHlsZTogUmVhY3QuQ1NTUHJvcGVydGllcyA9IHtcbiAgICBoZWlnaHQ6ICc5NXZoJyxcbiAgICB3aWR0aDogJzk1dmgnLFxuICAgIGJhY2tncm91bmRDb2xvcjogJyM2ZDZkNmQnLFxuICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICBmbGV4RGlyZWN0aW9uOiAnY29sdW1uJyxcbiAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgb3ZlcmZsb3c6ICdoaWRkZW4nXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgc3R5bGU9e2NvbnRhaW5lclN0eWxlfT5cbiAgICAgIHsvKiDkuIDnuqflrrnlmajlhoXlrrnljLrln58gKi99XG4gICAgPC9kaXY+XG4gIClcbn1cblxuZXhwb3J0IGRlZmF1bHQgQ29tcG9uZXRBMVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiQ29tcG9uZXRBMSIsImNvbnRhaW5lclN0eWxlIiwiaGVpZ2h0Iiwid2lkdGgiLCJiYWNrZ3JvdW5kQ29sb3IiLCJkaXNwbGF5IiwiZmxleERpcmVjdGlvbiIsImp1c3RpZnlDb250ZW50IiwiYWxpZ25JdGVtcyIsIm92ZXJmbG93IiwiZGl2Iiwic3R5bGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./frontend/componets/componet/componetA1.tsx\n");

/***/ }),

/***/ "(ssr)/./frontend/componets/componet/componetB1.tsx":
/*!****************************************************!*\
  !*** ./frontend/componets/componet/componetB1.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _features_feature_featureB1_1__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/feature/featureB1_1 */ \"(ssr)/./frontend/features/feature/featureB1_1.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst ComponetB1 = ({ isVisible })=>{\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [containerDimensions, setContainerDimensions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    // 监听容器尺寸变化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ComponetB1.useEffect\": ()=>{\n            const updateDimensions = {\n                \"ComponetB1.useEffect.updateDimensions\": ()=>{\n                    if (containerRef.current) {\n                        const { offsetWidth, offsetHeight } = containerRef.current;\n                        setContainerDimensions({\n                            width: offsetWidth,\n                            height: offsetHeight\n                        });\n                    }\n                }\n            }[\"ComponetB1.useEffect.updateDimensions\"];\n            updateDimensions();\n            window.addEventListener('resize', updateDimensions);\n            return ({\n                \"ComponetB1.useEffect\": ()=>window.removeEventListener('resize', updateDimensions)\n            })[\"ComponetB1.useEffect\"];\n        }\n    }[\"ComponetB1.useEffect\"], [\n        isVisible\n    ]);\n    const containerStyle = {\n        height: '100%',\n        width: '100%',\n        backgroundColor: '#6d6d6d',\n        display: isVisible ? 'flex' : 'none',\n        flexDirection: 'column',\n        justifyContent: 'flex-start',\n        alignItems: 'center',\n        overflow: 'hidden',\n        position: 'relative'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        style: containerStyle,\n        children: isVisible && containerDimensions.width > 0 && containerDimensions.height > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_feature_featureB1_1__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            containerHeight: containerDimensions.height,\n            containerWidth: containerDimensions.width\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/componet/componetB1.tsx\",\n            lineNumber: 45,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/componet/componetB1.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ComponetB1);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./frontend/componets/componet/componetB1.tsx\n");

/***/ }),

/***/ "(ssr)/./frontend/componets/componet/componetB2.tsx":
/*!****************************************************!*\
  !*** ./frontend/componets/componet/componetB2.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst ComponetB2 = ({ isVisible })=>{\n    const containerStyle = {\n        height: '100%',\n        width: '100%',\n        backgroundColor: '#6d6d6d',\n        display: isVisible ? 'flex' : 'none',\n        flexDirection: 'column',\n        justifyContent: 'center',\n        alignItems: 'center',\n        overflow: 'hidden'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: containerStyle\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/componet/componetB2.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ComponetB2);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9mcm9udGVuZC9jb21wb25ldHMvY29tcG9uZXQvY29tcG9uZXRCMi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRXlCO0FBTXpCLE1BQU1DLGFBQXdDLENBQUMsRUFBRUMsU0FBUyxFQUFFO0lBQzFELE1BQU1DLGlCQUFzQztRQUMxQ0MsUUFBUTtRQUNSQyxPQUFPO1FBQ1BDLGlCQUFpQjtRQUNqQkMsU0FBU0wsWUFBWSxTQUFTO1FBQzlCTSxlQUFlO1FBQ2ZDLGdCQUFnQjtRQUNoQkMsWUFBWTtRQUNaQyxVQUFVO0lBQ1o7SUFFQSxxQkFDRSw4REFBQ0M7UUFBSUMsT0FBT1Y7Ozs7OztBQUloQjtBQUVBLGlFQUFlRixVQUFVQSxFQUFBIiwic291cmNlcyI6WyIvVXNlcnMvemhhb196aV9mZW5nL0Rlc2t0b3Av5Luj56CB5paH5Lu2L0x5cmljV3JpdGluZ1Byb2plY3QvZnJvbnRlbmQvY29tcG9uZXRzL2NvbXBvbmV0L2NvbXBvbmV0QjIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXG5cbmludGVyZmFjZSBDb21wb25ldEIyUHJvcHMge1xuICBpc1Zpc2libGU6IGJvb2xlYW5cbn1cblxuY29uc3QgQ29tcG9uZXRCMjogUmVhY3QuRkM8Q29tcG9uZXRCMlByb3BzPiA9ICh7IGlzVmlzaWJsZSB9KSA9PiB7XG4gIGNvbnN0IGNvbnRhaW5lclN0eWxlOiBSZWFjdC5DU1NQcm9wZXJ0aWVzID0ge1xuICAgIGhlaWdodDogJzEwMCUnLFxuICAgIHdpZHRoOiAnMTAwJScsXG4gICAgYmFja2dyb3VuZENvbG9yOiAnIzZkNmQ2ZCcsXG4gICAgZGlzcGxheTogaXNWaXNpYmxlID8gJ2ZsZXgnIDogJ25vbmUnLFxuICAgIGZsZXhEaXJlY3Rpb246ICdjb2x1bW4nLFxuICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcbiAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICBvdmVyZmxvdzogJ2hpZGRlbidcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBzdHlsZT17Y29udGFpbmVyU3R5bGV9PlxuICAgICAgey8qIOS4muWKoeWuueWZqOWGheWuueWMuuWfnyAqL31cbiAgICA8L2Rpdj5cbiAgKVxufVxuXG5leHBvcnQgZGVmYXVsdCBDb21wb25ldEIyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJDb21wb25ldEIyIiwiaXNWaXNpYmxlIiwiY29udGFpbmVyU3R5bGUiLCJoZWlnaHQiLCJ3aWR0aCIsImJhY2tncm91bmRDb2xvciIsImRpc3BsYXkiLCJmbGV4RGlyZWN0aW9uIiwianVzdGlmeUNvbnRlbnQiLCJhbGlnbkl0ZW1zIiwib3ZlcmZsb3ciLCJkaXYiLCJzdHlsZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./frontend/componets/componet/componetB2.tsx\n");

/***/ }),

/***/ "(ssr)/./frontend/componets/componet/componetButton.tsx":
/*!********************************************************!*\
  !*** ./frontend/componets/componet/componetButton.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ButtonCodes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../ButtonCodes */ \"(ssr)/./frontend/ButtonCodes/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst ComponetButton = ({ onModeClick, onBusinessClick, isModeActive, isBusinessActive })=>{\n    const containerStyle = {\n        height: '3vh',\n        width: '20vw',\n        backgroundColor: 'transparent',\n        display: 'flex',\n        flexDirection: 'row',\n        justifyContent: 'center',\n        alignItems: 'center',\n        overflow: 'hidden',\n        position: 'absolute',\n        top: '0',\n        left: '0'\n    };\n    const buttonStyle = {\n        height: '100%',\n        width: '50%'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: containerStyle,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ButtonCodes__WEBPACK_IMPORTED_MODULE_2__.SecondaryButton, {\n                mode: \"toggle\",\n                onClick: onModeClick,\n                disabled: isModeActive,\n                initialActive: isModeActive,\n                customStyles: buttonStyle,\n                text: \"模式\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/componet/componetButton.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ButtonCodes__WEBPACK_IMPORTED_MODULE_2__.SecondaryButton, {\n                mode: \"toggle\",\n                onClick: onBusinessClick,\n                disabled: isBusinessActive,\n                initialActive: isBusinessActive,\n                customStyles: buttonStyle,\n                text: \"业务\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/componet/componetButton.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/componet/componetButton.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ComponetButton);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./frontend/componets/componet/componetButton.tsx\n");

/***/ }),

/***/ "(ssr)/./frontend/componets/componet/main_container.tsx":
/*!********************************************************!*\
  !*** ./frontend/componets/componet/main_container.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _componetA1__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./componetA1 */ \"(ssr)/./frontend/componets/componet/componetA1.tsx\");\n/* harmony import */ var _interaction_componet_interactionB__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../interaction/componet_interactionB */ \"(ssr)/./frontend/componets/interaction/componet_interactionB.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst MainContainer = ()=>{\n    const containerStyle = {\n        height: '100vh',\n        width: '100vw',\n        backgroundColor: '#242424',\n        display: 'flex',\n        flexDirection: 'row',\n        justifyContent: 'center',\n        alignItems: 'center',\n        overflow: 'hidden'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: containerStyle,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_componetA1__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/componet/main_container.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_interaction_componet_interactionB__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/componet/main_container.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/componet/main_container.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MainContainer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9mcm9udGVuZC9jb21wb25ldHMvY29tcG9uZXQvbWFpbl9jb250YWluZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRXlCO0FBQ1k7QUFDa0M7QUFFdkUsTUFBTUcsZ0JBQTBCO0lBQzlCLE1BQU1DLGlCQUFzQztRQUMxQ0MsUUFBUTtRQUNSQyxPQUFPO1FBQ1BDLGlCQUFpQjtRQUNqQkMsU0FBUztRQUNUQyxlQUFlO1FBQ2ZDLGdCQUFnQjtRQUNoQkMsWUFBWTtRQUNaQyxVQUFVO0lBQ1o7SUFFQSxxQkFDRSw4REFBQ0M7UUFBSUMsT0FBT1Y7OzBCQUNWLDhEQUFDSCxtREFBVUE7Ozs7OzBCQUNYLDhEQUFDQywwRUFBb0JBOzs7Ozs7Ozs7OztBQUczQjtBQUVBLGlFQUFlQyxhQUFhQSxFQUFBIiwic291cmNlcyI6WyIvVXNlcnMvemhhb196aV9mZW5nL0Rlc2t0b3Av5Luj56CB5paH5Lu2L0x5cmljV3JpdGluZ1Byb2plY3QvZnJvbnRlbmQvY29tcG9uZXRzL2NvbXBvbmV0L21haW5fY29udGFpbmVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0J1xuaW1wb3J0IENvbXBvbmV0QTEgZnJvbSAnLi9jb21wb25ldEExJ1xuaW1wb3J0IENvbXBvbmV0SW50ZXJhY3Rpb25CIGZyb20gJy4uL2ludGVyYWN0aW9uL2NvbXBvbmV0X2ludGVyYWN0aW9uQidcblxuY29uc3QgTWFpbkNvbnRhaW5lcjogUmVhY3QuRkMgPSAoKSA9PiB7XG4gIGNvbnN0IGNvbnRhaW5lclN0eWxlOiBSZWFjdC5DU1NQcm9wZXJ0aWVzID0ge1xuICAgIGhlaWdodDogJzEwMHZoJyxcbiAgICB3aWR0aDogJzEwMHZ3JyxcbiAgICBiYWNrZ3JvdW5kQ29sb3I6ICcjMjQyNDI0JyxcbiAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgZmxleERpcmVjdGlvbjogJ3JvdycsXG4gICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgIG92ZXJmbG93OiAnaGlkZGVuJ1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IHN0eWxlPXtjb250YWluZXJTdHlsZX0+XG4gICAgICA8Q29tcG9uZXRBMSAvPlxuICAgICAgPENvbXBvbmV0SW50ZXJhY3Rpb25CIC8+XG4gICAgPC9kaXY+XG4gIClcbn1cblxuZXhwb3J0IGRlZmF1bHQgTWFpbkNvbnRhaW5lclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiQ29tcG9uZXRBMSIsIkNvbXBvbmV0SW50ZXJhY3Rpb25CIiwiTWFpbkNvbnRhaW5lciIsImNvbnRhaW5lclN0eWxlIiwiaGVpZ2h0Iiwid2lkdGgiLCJiYWNrZ3JvdW5kQ29sb3IiLCJkaXNwbGF5IiwiZmxleERpcmVjdGlvbiIsImp1c3RpZnlDb250ZW50IiwiYWxpZ25JdGVtcyIsIm92ZXJmbG93IiwiZGl2Iiwic3R5bGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./frontend/componets/componet/main_container.tsx\n");

/***/ }),

/***/ "(ssr)/./frontend/componets/interaction/componet_interactionB.tsx":
/*!******************************************************************!*\
  !*** ./frontend/componets/interaction/componet_interactionB.tsx ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Store_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../Store/store */ \"(ssr)/./frontend/Store/store.ts\");\n/* harmony import */ var _componet_componetB1__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../componet/componetB1 */ \"(ssr)/./frontend/componets/componet/componetB1.tsx\");\n/* harmony import */ var _componet_componetB2__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../componet/componetB2 */ \"(ssr)/./frontend/componets/componet/componetB2.tsx\");\n/* harmony import */ var _componet_componetButton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../componet/componetButton */ \"(ssr)/./frontend/componets/componet/componetButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst ComponetInteractionB = ()=>{\n    const { activeContainer, setActiveContainer } = (0,_Store_store__WEBPACK_IMPORTED_MODULE_2__.useContainerStore)();\n    const handleModeClick = ()=>{\n        if (activeContainer !== 'mode') {\n            setActiveContainer('mode');\n        }\n    };\n    const handleBusinessClick = ()=>{\n        if (activeContainer !== 'business') {\n            setActiveContainer('business');\n        }\n    };\n    const containerStyle = {\n        position: 'relative',\n        height: '95vh',\n        width: '20vw',\n        marginLeft: '1vw'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: containerStyle,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_componet_componetB1__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isVisible: activeContainer === 'mode'\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/interaction/componet_interactionB.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_componet_componetB2__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isVisible: activeContainer === 'business'\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/interaction/componet_interactionB.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_componet_componetButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onModeClick: handleModeClick,\n                onBusinessClick: handleBusinessClick,\n                isModeActive: activeContainer === 'mode',\n                isBusinessActive: activeContainer === 'business'\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/interaction/componet_interactionB.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/componets/interaction/componet_interactionB.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ComponetInteractionB);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./frontend/componets/interaction/componet_interactionB.tsx\n");

/***/ }),

/***/ "(ssr)/./frontend/features/feature/featureB1_1.tsx":
/*!***************************************************!*\
  !*** ./frontend/features/feature/featureB1_1.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ButtonCodes_secondary_SecondaryButton_SecondaryButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/ButtonCodes/secondary/SecondaryButton/SecondaryButton */ \"(ssr)/./frontend/ButtonCodes/secondary/SecondaryButton/SecondaryButton.tsx\");\n/* harmony import */ var _features_logic_logicB1_1__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/logic/logicB1_1 */ \"(ssr)/./frontend/features/logic/logicB1_1.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst FeatureB1_1 = ({ containerHeight, containerWidth })=>{\n    const { coordinateButtonActive, setInitializeButton, setCoordinateButton } = (0,_features_logic_logicB1_1__WEBPACK_IMPORTED_MODULE_3__.useMatrixStore)();\n    // 容器样式\n    const containerStyle = {\n        position: 'relative',\n        height: `${containerHeight * 0.15}px`,\n        width: `${containerWidth * 0.94}px`,\n        backgroundColor: '#bebebe',\n        overflow: 'hidden',\n        top: `${containerHeight * 0.06}px` // 顶部对齐：占componetB1容器高的6%\n    };\n    // 文本样式\n    const textStyle = {\n        position: 'absolute',\n        top: `${containerHeight * 0.15 * 0.2}px`,\n        left: `${containerWidth * 0.94 * 0.01}px`,\n        fontSize: '30px',\n        color: '#242424'\n    };\n    // 初始化按键样式\n    const initButtonStyle = {\n        position: 'absolute',\n        top: `${containerHeight * 0.15 * 0.55}px`,\n        left: `${containerWidth * 0.94 * 0.04}px`,\n        height: `${containerHeight * 0.15 * 0.35}px`,\n        width: `${containerWidth * 0.94 * 0.44}px` // 键宽：占featureB1_1容器宽的44%\n    };\n    // 坐标按键样式\n    const coordButtonStyle = {\n        position: 'absolute',\n        top: `${containerHeight * 0.15 * 0.55}px`,\n        right: `${containerWidth * 0.94 * 0.04}px`,\n        height: `${containerHeight * 0.15 * 0.35}px`,\n        width: `${containerWidth * 0.94 * 0.44}px` // 键宽：占featureB1_1容器宽的44%\n    };\n    // 初始化按键点击处理\n    const handleInitializeClick = (isActive)=>{\n        setInitializeButton(isActive);\n        if (isActive) {\n            setCoordinateButton(false); // 重置坐标按键状态为false\n        }\n    };\n    // 坐标按键点击处理\n    const handleCoordinateClick = (isActive)=>{\n        setCoordinateButton(isActive);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: containerStyle,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: textStyle,\n                children: \"矩阵\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/features/feature/featureB1_1.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: initButtonStyle,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ButtonCodes_secondary_SecondaryButton_SecondaryButton__WEBPACK_IMPORTED_MODULE_2__.SecondaryButton, {\n                    mode: \"instant\",\n                    text: \"初始化\",\n                    onClick: handleInitializeClick,\n                    customStyles: {\n                        width: '100%',\n                        height: '100%',\n                        fontSize: 'inherit' // 尺寸自适应\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/features/feature/featureB1_1.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/features/feature/featureB1_1.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: coordButtonStyle,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ButtonCodes_secondary_SecondaryButton_SecondaryButton__WEBPACK_IMPORTED_MODULE_2__.SecondaryButton, {\n                    mode: \"toggle\",\n                    text: \"坐标\",\n                    initialActive: coordinateButtonActive,\n                    onClick: handleCoordinateClick,\n                    customStyles: {\n                        width: '100%',\n                        height: '100%',\n                        fontSize: 'inherit' // 尺寸自适应\n                    }\n                }, `coordinate-${coordinateButtonActive}`, false, {\n                    fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/features/feature/featureB1_1.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/features/feature/featureB1_1.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/代码文件/LyricWritingProject/frontend/features/feature/featureB1_1.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FeatureB1_1);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./frontend/features/feature/featureB1_1.tsx\n");

/***/ }),

/***/ "(ssr)/./frontend/features/logic/logicB1_1.ts":
/*!**********************************************!*\
  !*** ./frontend/features/logic/logicB1_1.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMatrixStore: () => (/* binding */ useMatrixStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n\n// 创建矩阵功能状态管理store\nconst useMatrixStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set)=>({\n        // 初始状态\n        initializeButtonActive: false,\n        coordinateButtonActive: false,\n        // 设置初始化按键状态\n        setInitializeButton: (active)=>set((state)=>({\n                    initializeButtonActive: active,\n                    // 当初始化按键激活时，重置坐标按键状态\n                    coordinateButtonActive: active ? false : state.coordinateButtonActive\n                })),\n        // 设置坐标按键状态\n        setCoordinateButton: (active)=>set({\n                coordinateButtonActive: active\n            }),\n        // 重置所有按键状态\n        resetAllButtons: ()=>set({\n                initializeButtonActive: false,\n                coordinateButtonActive: false\n            })\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./frontend/features/logic/logicB1_1.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Ffrontend%2Fcomponets%2Fcomponet%2Fmain_container.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Ffrontend%2Fcomponets%2Fcomponet%2Fmain_container.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./frontend/componets/componet/main_container.tsx */ \"(ssr)/./frontend/componets/componet/main_container.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGemhhb196aV9mZW5nJTJGRGVza3RvcCUyRiVFNCVCQiVBMyVFNyVBMCU4MSVFNiU5NiU4NyVFNCVCQiVCNiUyRkx5cmljV3JpdGluZ1Byb2plY3QlMkZmcm9udGVuZCUyRmNvbXBvbmV0cyUyRmNvbXBvbmV0JTJGbWFpbl9jb250YWluZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNE1BQXFLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiL1VzZXJzL3poYW9femlfZmVuZy9EZXNrdG9wL+S7o+eggeaWh+S7ti9MeXJpY1dyaXRpbmdQcm9qZWN0L2Zyb250ZW5kL2NvbXBvbmV0cy9jb21wb25ldC9tYWluX2NvbnRhaW5lci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Ffrontend%2Fcomponets%2Fcomponet%2Fmain_container.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(ssr)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(ssr)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/dynamic-access-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/server/app-render/dynamic-access-async-storage.external.js" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/dynamic-access-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/is-bot":
/*!***********************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/is-bot" ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-bot");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/zustand"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fzhao_zi_feng%2FDesktop%2F%E4%BB%A3%E7%A0%81%E6%96%87%E4%BB%B6%2FLyricWritingProject&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();