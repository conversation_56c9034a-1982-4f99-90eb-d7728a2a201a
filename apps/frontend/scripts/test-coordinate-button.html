<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>坐标按键状态测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .status-display {
            background-color: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
        }
        .button-test {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-button {
            background-color: #e0e0e0;
            border: 1px solid #999;
            padding: 10px 20px;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.2s;
        }
        .test-button.active {
            background-color: #4CAF50;
            color: white;
        }
        .test-button:hover {
            background-color: #d0d0d0;
        }
        .test-button.active:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>坐标按键状态切换测试</h1>
        
        <div class="status-display">
            <strong>当前状态：</strong>
            <span id="status">未激活</span>
        </div>
        
        <div class="button-test">
            <h3>模拟坐标按键（Toggle模式）</h3>
            <button id="coordinate-btn" class="test-button">坐标</button>
            <p>点击按键应该在激活/未激活状态之间切换</p>
        </div>
        
        <div class="status-display">
            <strong>点击历史：</strong>
            <div id="click-history"></div>
        </div>
        
        <div class="button-test">
            <h3>重置按键</h3>
            <button id="reset-btn" class="test-button">重置状态</button>
            <p>模拟初始化按键的重置功能</p>
        </div>
    </div>

    <script>
        let isActive = false;
        let clickCount = 0;
        
        const coordinateBtn = document.getElementById('coordinate-btn');
        const statusSpan = document.getElementById('status');
        const clickHistory = document.getElementById('click-history');
        const resetBtn = document.getElementById('reset-btn');
        
        function updateStatus() {
            statusSpan.textContent = isActive ? '激活' : '未激活';
            coordinateBtn.classList.toggle('active', isActive);
        }
        
        function addToHistory(action, newState) {
            clickCount++;
            const historyItem = document.createElement('div');
            historyItem.textContent = `${clickCount}. ${action} -> ${newState ? '激活' : '未激活'}`;
            clickHistory.appendChild(historyItem);
            clickHistory.scrollTop = clickHistory.scrollHeight;
        }
        
        coordinateBtn.addEventListener('click', () => {
            // 模拟toggle行为
            isActive = !isActive;
            updateStatus();
            addToHistory('点击坐标按键', isActive);
            console.log('坐标按键点击，新状态:', isActive);
        });
        
        resetBtn.addEventListener('click', () => {
            // 模拟初始化按键的重置行为
            isActive = false;
            updateStatus();
            addToHistory('重置按键', isActive);
            console.log('重置按键点击，状态重置为:', isActive);
        });
        
        // 初始化状态
        updateStatus();
    </script>
</body>
</html>
