'use client';

import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { secondaryButtonStyles, DEFAULT_BUTTON_TEXT } from '../style/style_secondary';
import { ButtonMode } from '../event/event_secondary';

// 组件属性接口
export interface SecondaryButtonProps {
  // 按键模式：持续状态或瞬时状态
  mode?: ButtonMode;
  // 初始激活状态
  initialActive?: boolean;
  // 按键文本
  text?: string;
  // 事件处理器
  onClick?: (isActive: boolean) => void;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
  onMouseDown?: () => void;
  onMouseUp?: () => void;
  // 自定义样式
  customStyles?: React.CSSProperties;
  // 是否禁用
  disabled?: boolean;
}

// 普通按键组件
export const SecondaryButton: React.FC<SecondaryButtonProps> = ({
  mode = 'toggle',
  initialActive = false,
  text = DEFAULT_BUTTON_TEXT,
  onClick,
  onMouseEnter,
  onMouseLeave,
  onMouseDown,
  onMouseUp,
  customStyles = {},
  disabled = false,
}) => {
  // 内部状态管理
  const [isActive, setIsActive] = useState(initialActive);
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);

  // 同步外部 initialActive 属性的变化
  useEffect(() => {
    setIsActive(initialActive);
  }, [initialActive]);





  // 计算当前样式
  const currentStyles = useMemo(() => {
    const baseStyles = { ...secondaryButtonStyles.default };

    let backgroundColor = baseStyles.backgroundColor;

    if (mode === 'toggle') {
      // 持续状态模式
      if (isActive) {
        backgroundColor = secondaryButtonStyles.toggle.active.backgroundColor;
        if (isHovered && !disabled) {
          backgroundColor = secondaryButtonStyles.toggle.active.hover;
        }
      } else {
        backgroundColor = secondaryButtonStyles.toggle.inactive.backgroundColor;
        if (isHovered && !disabled) {
          backgroundColor = secondaryButtonStyles.toggle.inactive.hover;
        }
      }
    } else if (mode === 'instant') {
      // 瞬时状态模式
      if (isPressed && !disabled) {
        backgroundColor = secondaryButtonStyles.instant.active;
      } else if (isHovered && !disabled) {
        backgroundColor = secondaryButtonStyles.instant.hover;
      } else {
        backgroundColor = secondaryButtonStyles.instant.default;
      }
    }

    // 应用禁用状态的样式修改
    const finalStyles = {
      ...baseStyles,
      ...customStyles,
      backgroundColor,
    };

    if (disabled) {
      finalStyles.cursor = 'not-allowed';
      finalStyles.opacity = 0.8; // 稍微降低透明度但仍能看清激活颜色
    }

    return finalStyles;
  }, [mode, isActive, isHovered, isPressed, disabled, customStyles]);

  // 事件处理函数
  const handleClick = useCallback(() => {
    if (disabled) return;

    if (mode === 'toggle') {
      // 直接在组件中处理toggle逻辑
      const newActiveState = !isActive;
      setIsActive(newActiveState);
      onClick?.(newActiveState);
    } else if (mode === 'instant') {
      // instant模式
      setIsActive(true);
      onClick?.(true);
    }
  }, [disabled, mode, isActive, onClick]);

  const handleMouseEnter = useCallback(() => {
    if (disabled) return;

    setIsHovered(true);
    onMouseEnter?.();
  }, [disabled, onMouseEnter]);

  const handleMouseLeave = useCallback(() => {
    if (disabled) return;

    setIsHovered(false);
    setIsPressed(false);
    onMouseLeave?.();
  }, [disabled, onMouseLeave]);

  const handleMouseDown = useCallback(() => {
    if (disabled) return;

    setIsPressed(true);
    if (mode === 'instant') {
      setIsActive(true);
    }
    onMouseDown?.();
  }, [disabled, mode, onMouseDown]);

  const handleMouseUp = useCallback(() => {
    if (disabled) return;

    setIsPressed(false);
    if (mode === 'instant') {
      setIsActive(false);
    }
    onMouseUp?.();
  }, [disabled, mode, onMouseUp]);

  return (
    <button
      style={currentStyles}
      onClick={handleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      disabled={disabled}
      type="button"
    >
      {text}
    </button>
  );
};
