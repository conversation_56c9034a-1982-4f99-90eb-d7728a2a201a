// 普通按键事件类型定义
export type ButtonMode = 'toggle' | 'instant';

export interface SecondaryButtonState {
  isActive: boolean;
  mode: ButtonMode;
}

export interface SecondaryButtonEvents {
  onClick?: (isActive: boolean) => void;
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
  onMouseDown?: () => void;
  onMouseUp?: () => void;
}

// 普通按键事件处理类
export class SecondaryButtonEventHandler {
  private state: SecondaryButtonState;
  private events: SecondaryButtonEvents;

  constructor(
    initialState: SecondaryButtonState = { isActive: false, mode: 'toggle' },
    events: SecondaryButtonEvents = {}
  ) {
    this.state = initialState;
    this.events = events;
  }

  // 获取当前状态
  getState(): SecondaryButtonState {
    return { ...this.state };
  }

  // 设置模式
  setMode(mode: ButtonMode): void {
    this.state.mode = mode;
  }

  // 设置激活状态
  setActive(isActive: boolean): void {
    this.state.isActive = isActive;
  }

  // 处理点击事件
  handleClick(): boolean {
    if (this.state.mode === 'toggle') {
      // 持续状态模式：切换并保持状态
      this.state.isActive = !this.state.isActive;
    } else if (this.state.mode === 'instant') {
      // 瞬时状态模式：瞬间激活
      this.state.isActive = true;
    }

    // 调用外部点击事件
    if (this.events.onClick) {
      this.events.onClick(this.state.isActive);
    }

    return this.state.isActive;
  }

  // 处理鼠标进入事件
  handleMouseEnter(): void {
    if (this.events.onMouseEnter) {
      this.events.onMouseEnter();
    }
  }

  // 处理鼠标离开事件
  handleMouseLeave(): void {
    if (this.events.onMouseLeave) {
      this.events.onMouseLeave();
    }
  }

  // 处理鼠标按下事件
  handleMouseDown(): void {
    if (this.state.mode === 'instant') {
      this.state.isActive = true;
    }
    
    if (this.events.onMouseDown) {
      this.events.onMouseDown();
    }
  }

  // 处理鼠标松开事件
  handleMouseUp(): void {
    if (this.state.mode === 'instant') {
      this.state.isActive = false;
    }
    
    if (this.events.onMouseUp) {
      this.events.onMouseUp();
    }
  }

  // 更新事件处理器
  updateEvents(events: SecondaryButtonEvents): void {
    this.events = { ...this.events, ...events };
  }
}

// 默认事件处理器工厂函数
export const createSecondaryButtonEventHandler = (
  mode: ButtonMode = 'toggle',
  events: SecondaryButtonEvents = {},
  initialActive: boolean = false
): SecondaryButtonEventHandler => {
  return new SecondaryButtonEventHandler(
    { isActive: initialActive, mode },
    events
  );
};
