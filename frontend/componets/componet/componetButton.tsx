'use client'

import React from 'react'
import { SecondaryButton } from '../../ButtonCodes'

interface ComponetButtonProps {
  onModeClick: () => void
  onBusinessClick: () => void
  isModeActive: boolean
  isBusinessActive: boolean
}

const ComponetButton: React.FC<ComponetButtonProps> = ({
  onModeClick,
  onBusinessClick,
  isModeActive,
  isBusinessActive
}) => {
  const containerStyle: React.CSSProperties = {
    height: '3vh',
    width: '20vw',
    backgroundColor: 'transparent', // 透明背景
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    position: 'absolute',
    top: '0',
    left: '0'
  }

  const buttonStyle: React.CSSProperties = {
    height: '100%',
    width: '50%'
  }

  return (
    <div style={containerStyle}>
      <SecondaryButton
        mode="toggle"
        onClick={onModeClick}
        disabled={isModeActive}
        initialActive={isModeActive}
        customStyles={buttonStyle}
        text="模式"
      />
      <SecondaryButton
        mode="toggle"
        onClick={onBusinessClick}
        disabled={isBusinessActive}
        initialActive={isBusinessActive}
        customStyles={buttonStyle}
        text="业务"
      />
    </div>
  )
}

export default ComponetButton
